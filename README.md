# نور البيولوجي (Noor Biology)

A Flutter application for the Noor Biology platform with Arabic interface.

## Features

- **Authentication System**
  - Login with username/phone and password
  - Registration for new users
  - Persistent user session

- **Security Features**
  - Screenshot prevention for sensitive screens
  - Secure storage of user data

- **User Experience**
  - Splash screen
  - Loading screens/indicators
  - Offline detection and handling
  - Support for screen rotation
  - Arabic localization and RTL layout

## Project Structure

```
/lib
  /models        - Data models
  /providers     - State management using Provider
  /screens       - UI screens
  /services      - API and other services
  /utils         - Utility functions and constants
  /widgets       - Reusable UI components
  main.dart      - Application entry point
```

## API Integration

The app integrates with the following APIs:

- User Registration: `https://noorbiology.com/register_process.php`
- User Login: `https://noorbiology.com/sign-in_process.php`

## Setup and Running

1. Ensure you have Flutter installed on your machine
2. Clone the repository
3. Run `flutter pub get` to install dependencies
4. Run `flutter run` to start the application

## Dependencies

- Provider for state management
- SharedPreferences for local storage
- HTTP for API requests
- Connectivity Plus for network detection
- Flutter Window Manager for screenshot prevention
- Flutter SpinKit for loading animations

## Notes

- The app is configured for Arabic language and RTL layout
- All authentication screens prevent screenshots
- The app checks for network connectivity and shows an offline screen when needed
