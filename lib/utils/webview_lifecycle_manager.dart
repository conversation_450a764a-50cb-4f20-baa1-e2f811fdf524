
/// A utility class to help manage WebView lifecycle and prevent unnecessary reloads
/// when the app is temporarily hidden (like when pulling down the notification panel)
class WebViewLifecycleManager {
  /// The timestamp when the app was last paused
  static DateTime? _lastPausedTime;

  /// The timestamp when the app was last inactive (notification panel pulled down)
  static DateTime? _lastInactiveTime;

  /// Flag to track if we're in a notification panel pull state
  static bool _isInNotificationPull = false;

  /// The minimum duration (in milliseconds) that the app should be in background
  /// before considering it a "real" background event that requires a reload
  /// This helps distinguish between notification panel pulls and actual app backgrounding
  static const int _minBackgroundDuration =
      5000; // 5 seconds (increased from 2 seconds)

  /// Maximum time for notification panel interaction before considering it a different action
  static const int _maxNotificationPanelDuration = 10000; // 10 seconds

  /// Call this method when AppLifecycleState.paused is detected
  static void onAppPaused() {
    _lastPausedTime = DateTime.now();
  }

  /// Call this method when AppLifecycleState.inactive is detected
  /// This is typically when the notification panel is being pulled down
  static void onAppInactive() {
    _lastInactiveTime = DateTime.now();
    _isInNotificationPull = true;
  }

  /// Call this method when AppLifecycleState.resumed is detected
  /// Returns true if the WebView should be reloaded (app was in background for significant time)
  /// Returns false if the WebView should NOT be reloaded (likely just notification panel)
  static bool shouldReloadOnResume() {
    // If we were in a notification pull state and resumed quickly, definitely don't reload
    if (_isInNotificationPull && _lastInactiveTime != null) {
      final now = DateTime.now();
      final inactiveDuration =
          now.difference(_lastInactiveTime!).inMilliseconds;

      // If we resumed quickly from inactive state, it was almost certainly
      // just the notification panel - don't reload
      if (inactiveDuration < _maxNotificationPanelDuration) {
        _isInNotificationPull = false; // Reset the flag
        return false;
      }
    }

    // Reset notification pull state
    _isInNotificationPull = false;

    // Standard background check
    if (_lastPausedTime == null) {
      return false;
    }

    final now = DateTime.now();
    final pausedDuration = now.difference(_lastPausedTime!).inMilliseconds;

    // If the app was in background for less than the minimum duration,
    // it was likely just the notification panel being pulled down or a quick app switch
    return pausedDuration >= _minBackgroundDuration;
  }
}
