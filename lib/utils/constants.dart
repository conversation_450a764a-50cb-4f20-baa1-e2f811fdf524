import 'package:flutter/material.dart';

// App colors from noorbiology.com website
class AppColors {
  static const Color primary = Color(0xFFe19527);   // Golden color from website
  static const Color accent = Color(0xFFa7a8ad);    // Light gray from website
  static const Color background = Color(0xFFF5F5F5); // Light background from website
  static const Color darkText = Color(0xFF212121);  // Dark text
  static const Color lightText = Color(0xFF757575); // Light text
  static const Color error = Color(0xFFE53935);     // Error red
  static const Color backgroundLight = Color(0xFFebf0f3); // Light background shade
  static const Color lightAccent = Color(0xFFdfe4e6);    // Another light shade
}

// Text styles
class AppTextStyles {
  static const TextStyle headline = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.darkText,
  );

  static const TextStyle title = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.darkText,
  );

  static const TextStyle body = TextStyle(
    fontSize: 16,
    color: AppColors.darkText,
  );

  static const TextStyle caption = TextStyle(
    fontSize: 14,
    color: AppColors.lightText,
  );

  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );
}

// Input decoration
InputDecoration inputDecoration(String label) {
  return InputDecoration(
    labelText: label,
    labelStyle: AppTextStyles.body.copyWith(color: AppColors.lightText),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8.0),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8.0),
      borderSide: const BorderSide(color: AppColors.primary, width: 2.0),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8.0),
      borderSide: const BorderSide(color: AppColors.error, width: 1.0),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8.0),
      borderSide: const BorderSide(color: AppColors.error, width: 2.0),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
  );
}

// Button style
final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
  foregroundColor: Colors.white,
  backgroundColor: AppColors.primary,
  minimumSize: const Size(double.infinity, 50),
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(8.0),
  ),
);

// Screen padding
const EdgeInsets screenPadding = EdgeInsets.all(16.0);

// Vertical spacing
const Widget verticalSpaceSmall = SizedBox(height: 8.0);
const Widget verticalSpaceMedium = SizedBox(height: 16.0);
const Widget verticalSpaceLarge = SizedBox(height: 24.0);

// Horizontal spacing
const Widget horizontalSpaceSmall = SizedBox(width: 8.0);
const Widget horizontalSpaceMedium = SizedBox(width: 16.0);
const Widget horizontalSpaceLarge = SizedBox(width: 24.0);
