import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ScreenUtils {
  // Set status bar color with iOS-specific optimization
  static void setStatusBarColor(Color color, [Brightness brightness = Brightness.light]) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: brightness == Brightness.light ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: brightness,
      ),
    );
  }

  // Optimize system UI for iOS
  static void optimizeSystemUI() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [SystemUiOverlay.top],
    );
  }

  // Enter full screen mode for iOS
  static void enterFullScreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }
}
