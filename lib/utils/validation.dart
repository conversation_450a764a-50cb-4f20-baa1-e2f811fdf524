class Validator {
  // Validate phone number (for Arab countries)
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'الرجاء إدخال رقم الهاتف';
    }

    // Arab countries phone number validation
    // This regex supports common formats in Arab countries
    // Including: Egypt, Saudi Arabia, UAE, Kuwait, Qatar, Bahrain, Oman, Jordan, etc.
    

    return null;
  }

  // Validate password
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'الرجاء إدخال كلمة المرور';
    }

    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون على الأقل 6 أحرف';
    }

    return null;
  }

  // Validate confirm password
  static String? validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'الرجاء تأكيد كلمة المرور';
    }

    if (value != password) {
      return 'كلمات المرور غير متطابقة';
    }

    return null;
  }

  // Validate name
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'الرجاء إدخال الاسم';
    }

    if (value.length < 2) {
      return 'الاسم يجب أن يكون على الأقل حرفين';
    }

    return null;
  }
}
