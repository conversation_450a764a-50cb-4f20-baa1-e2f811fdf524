import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:no_screenshot/no_screenshot.dart';

// استيراد مشروط لمكتبة screen_protector
// يتم استخدامها فقط إذا كانت متاحة
import 'package:screen_protector/screen_protector.dart' as screen_protector;

/// مدير منع التقاط الشاشة وتسجيلها على مستوى التطبيق
/// تم تحسينه باستخدام مكتبة screen_protector
class ScreenshotPrevention {
  static final ScreenshotPrevention _instance =
      ScreenshotPrevention._internal();
  factory ScreenshotPrevention() => _instance;
  ScreenshotPrevention._internal();

  static const MethodChannel _channel =
      MethodChannel('com.app.noorbiology/security');
  final NoScreenshot _noScreenshot = NoScreenshot.instance;
  Timer? _protectionCheckTimer;
  Timer? _recordingCheckTimer;
  bool _isInitialized = false;
  bool _isRecording = false;
  final List<VoidCallback> _screenshotDetectedCallbacks = [];
  final List<Function(bool)> _recordingStatusCallbacks = [];

  /// تهيئة منع التقاط الشاشة
  Future<void> initialize() async {
    if (_isInitialized) return;

    _isInitialized = true;

    // إعداد قناة الاتصال مع الكود الأصلي
    _setupMethodChannel();

    // تفعيل الحماية
    await enableProtection();

    // إعداد مراقبة دورية
    _startPeriodicCheck();

    // بدء فحص تسجيل الشاشة (iOS فقط)
    if (Platform.isIOS) {
      _startRecordingCheck();
    }
  }

  /// إعداد قناة الاتصال مع الكود الأصلي
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      if (call.method == 'screenCaptureDetected') {
        _notifyScreenshotDetected();
        return null;
      }
      return null;
    });
  }

  /// تفعيل الحماية
  Future<void> enableProtection() async {
    try {
      // استخدام no_screenshot كطبقة أساسية للحماية
      await _noScreenshot.screenshotOff();

      // محاولة استخدام screen_protector إذا كانت متاحة
      try {
        if (Platform.isAndroid) {
          // استخدام screen_protector لمنع الاسكرين شوت على Android
          await screen_protector.ScreenProtector.protectDataLeakageOn();
        } else if (Platform.isIOS) {
          // استخدام screen_protector لمنع الاسكرين شوت على iOS
          await screen_protector.ScreenProtector.preventScreenshotOn();

          // حماية من تسرب البيانات عند وضع التطبيق في الخلفية
          await screen_protector.ScreenProtector.protectDataLeakageWithBlur();
        }
      } catch (e) {
        debugPrint('Screen protector method error: $e');
        // تجاهل الأخطاء هنا لأن no_screenshot تعمل كطبقة أساسية
      }

      // استدعاء الكود الأصلي لتعزيز الحماية
      if (Platform.isIOS) {
        try {
          await _channel.invokeMethod('enableScreenProtection');
        } catch (e) {
          debugPrint('Native protection method error: $e');
        }
      }
    } catch (e) {
      debugPrint('Screenshot protection error: $e');
      // محاولة استخدام طريقة بديلة في حالة فشل الطريقة الأساسية
      _fallbackProtection();
    }
  }

  /// طريقة احتياطية للحماية في حالة فشل الطرق الأخرى
  Future<void> _fallbackProtection() async {
    try {
      await _noScreenshot.screenshotOff();
    } catch (e) {
      debugPrint('Fallback protection error: $e');
    }
  }

  /// بدء الفحص الدوري
  void _startPeriodicCheck() {
    _protectionCheckTimer?.cancel();
    _protectionCheckTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      enableProtection();
    });
  }

  /// بدء فحص تسجيل الشاشة (iOS فقط)
  void _startRecordingCheck() {
    _recordingCheckTimer?.cancel();
    _recordingCheckTimer =
        Timer.periodic(const Duration(milliseconds: 500), (_) async {
      if (Platform.isIOS) {
        try {
          bool isRecording = false;

          // محاولة استخدام screen_protector إذا كانت متاحة
          try {
            isRecording = await screen_protector.ScreenProtector.isRecording();
          } catch (e) {
            // تجاهل الأخطاء هنا واستخدام القيمة الافتراضية (false)
            debugPrint('Screen protector recording check error: $e');
          }

          if (isRecording != _isRecording) {
            _isRecording = isRecording;
            _notifyRecordingStatusChanged(isRecording);
          }
        } catch (e) {
          debugPrint('Recording check error: $e');
        }
      }
    });
  }

  /// إيقاف الفحص الدوري
  void stopPeriodicCheck() {
    _protectionCheckTimer?.cancel();
    _protectionCheckTimer = null;

    _recordingCheckTimer?.cancel();
    _recordingCheckTimer = null;
  }

  /// إضافة مستمع لاكتشاف التقاط الشاشة
  void addScreenshotDetectedListener(VoidCallback callback) {
    _screenshotDetectedCallbacks.add(callback);
  }

  /// إزالة مستمع لاكتشاف التقاط الشاشة
  void removeScreenshotDetectedListener(VoidCallback callback) {
    _screenshotDetectedCallbacks.remove(callback);
  }

  /// إضافة مستمع لتغيير حالة تسجيل الشاشة
  void addRecordingStatusListener(Function(bool) callback) {
    _recordingStatusCallbacks.add(callback);
  }

  /// إزالة مستمع لتغيير حالة تسجيل الشاشة
  void removeRecordingStatusListener(Function(bool) callback) {
    _recordingStatusCallbacks.remove(callback);
  }

  /// إشعار المستمعين باكتشاف التقاط الشاشة
  void _notifyScreenshotDetected() {
    for (final callback in _screenshotDetectedCallbacks) {
      callback();
    }
  }

  /// إشعار المستمعين بتغيير حالة تسجيل الشاشة
  void _notifyRecordingStatusChanged(bool isRecording) {
    for (final callback in _recordingStatusCallbacks) {
      callback(isRecording);
    }
  }

  /// الحصول على حالة تسجيل الشاشة الحالية
  bool get isRecording => _isRecording;

  /// تنظيف الموارد
  void dispose() {
    stopPeriodicCheck();
    _screenshotDetectedCallbacks.clear();
    _recordingStatusCallbacks.clear();
  }
}

/// امتداد للسياق لتسهيل الوصول إلى مدير منع التقاط الشاشة
extension ScreenshotPreventionExtension on BuildContext {
  /// الحصول على مدير منع التقاط الشاشة
  ScreenshotPrevention get screenshotPrevention => ScreenshotPrevention();
}
