import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:no_screenshot/no_screenshot.dart';

/// أداة اختبار لفحص فعالية منع التقاط الشاشة وتسجيلها
class ScreenSecurityTest {
  static const MethodChannel _channel = MethodChannel('com.app.noorbiology/security');
  static final NoScreenshot _noScreenshot = NoScreenshot.instance;
  static bool _isTestRunning = false;
  static int _detectedAttempts = 0;
  static Timer? _testTimer;
  static VoidCallback? _onDetectionCallback;

  /// بدء اختبار الحماية
  static Future<void> startTest(BuildContext context, {VoidCallback? onDetection}) async {
    if (_isTestRunning) return;
    
    _isTestRunning = true;
    _detectedAttempts = 0;
    _onDetectionCallback = onDetection;
    
    // إعداد مستمع لاكتشاف محاولات التقاط الشاشة
    _setupDetectionListener();
    
    // تفعيل الحماية
    await _enableProtection();
    
    // عرض رسالة للمستخدم
    _showTestStartedDialog(context);
    
    // بدء مؤقت لإنهاء الاختبار بعد 30 ثانية
    _testTimer = Timer(const Duration(seconds: 30), () {
      stopTest(context);
    });
  }
  
  /// إيقاف اختبار الحماية
  static Future<void> stopTest(BuildContext context) async {
    if (!_isTestRunning) return;
    
    _isTestRunning = false;
    _testTimer?.cancel();
    _testTimer = null;
    
    // عرض نتائج الاختبار
    _showTestResultsDialog(context);
  }
  
  /// إعداد مستمع لاكتشاف محاولات التقاط الشاشة
  static void _setupDetectionListener() {
    _channel.setMethodCallHandler((call) async {
      if (call.method == 'screenCaptureDetected') {
        _detectedAttempts++;
        if (_onDetectionCallback != null) {
          _onDetectionCallback!();
        }
        return null;
      }
      return null;
    });
  }
  
  /// تفعيل الحماية
  static Future<void> _enableProtection() async {
    try {
      // استخدام حزمة no_screenshot لمنع الاسكرين شوت
      await _noScreenshot.screenshotOff();
      
      // استدعاء الكود الأصلي لتعزيز الحماية
      if (Platform.isIOS) {
        try {
          await _channel.invokeMethod('enableScreenProtection');
        } catch (e) {
          debugPrint('Native protection method error: $e');
        }
      }
    } catch (e) {
      debugPrint('Screenshot protection error: $e');
    }
  }
  
  /// عرض رسالة بدء الاختبار
  static void _showTestStartedDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('اختبار الحماية'),
        content: const Text(
          'تم بدء اختبار حماية الشاشة. يرجى محاولة:\n'
          '1. التقاط صورة للشاشة\n'
          '2. تسجيل الشاشة\n'
          'سيتم إنهاء الاختبار تلقائيًا بعد 30 ثانية.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
  
  /// عرض نتائج الاختبار
  static void _showTestResultsDialog(BuildContext context) {
    String resultMessage;
    Color resultColor;
    
    if (_detectedAttempts > 0) {
      resultMessage = 'تم اكتشاف $_detectedAttempts محاولة لالتقاط الشاشة أو تسجيلها.\n'
          'الحماية تعمل بشكل صحيح!';
      resultColor = Colors.green;
    } else {
      resultMessage = 'لم يتم اكتشاف أي محاولات لالتقاط الشاشة.\n'
          'إما أنك لم تحاول التقاط الشاشة، أو أن الحماية لا تعمل بشكل صحيح.';
      resultColor = Colors.orange;
    }
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('نتائج اختبار الحماية'),
        content: Text(
          resultMessage,
          style: TextStyle(color: resultColor),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
  
  /// الحصول على عدد محاولات التقاط الشاشة المكتشفة
  static int getDetectedAttempts() {
    return _detectedAttempts;
  }
  
  /// التحقق مما إذا كان الاختبار قيد التشغيل
  static bool isTestRunning() {
    return _isTestRunning;
  }
}
