import 'package:flutter/material.dart';
import '../services/logger_service.dart';

class ErrorHandler {
  static void handleError(BuildContext context, dynamic error, {String? customMessage}) {
    String message;
    
    // Handle different types of errors
    if (error.toString().contains('SocketException') || 
        error.toString().contains('Connection refused')) {
      message = 'لا يمكن الاتصال بالخادم. تحقق من اتصال الإنترنت';
    } 
    else if (error.toString().contains('TimeoutException')) {
      message = 'انتهت مهلة الاتصال. حاول مرة أخرى';
    }
    else if (error.toString().contains('WebResourceError')) {
      message = 'حدث خطأ أثناء تحميل الصفحة. حاول مرة أخرى';
    }
    else if (error.toString().contains('401')) {
      message = 'جلسة منتهية. يرجى تسجيل الدخول مرة أخرى';
    }
    else {
      message = customMessage ?? 'حدث خطأ غير متوقع. حاول مرة أخرى';
    }

    // Log the error
    logger.e('Error occurred', error, StackTrace.current);

    // Show snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
