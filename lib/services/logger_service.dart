import 'package:logger/logger.dart';

/// A service class for logging messages throughout the app.
class LoggerService {
  static final LoggerService _instance = LoggerService._internal();
  
  factory LoggerService() => _instance;
  
  late Logger _logger;
  
  LoggerService._internal() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,        // Number of method calls to display
        errorMethodCount: 8,   // Number of method calls if error occurs
        lineLength: 120,       // Width of the output
        colors: true,          // Colorful log messages
        printEmojis: true,     // Print an emoji for each log message
        printTime: true,       // Should print the time for each log
      ),
      // Filter messages by level (e.g., in production you might want to hide debug logs)
      level: Level.verbose,    // Show all logs in development
    );
  }

  /// Log a debug message.
  void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log an info message.
  void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message.
  void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log an error message.
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log a verbose message.
  void v(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.v(message, error: error, stackTrace: stackTrace);
  }

  /// Log a wtf (what a terrible failure) message.
  void wtf(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }
}

// Global instance for easy access throughout the app
final logger = LoggerService();
