import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:noorbiology/utils/error_handler.dart';
import '../models/user_model.dart';
import 'logger_service.dart';
import 'package:flutter/material.dart';

class ApiService {
  static const String baseUrl = 'https://noorbiology.com';
  
  ApiService() {
    logger.i('ApiService initialized with base URL: $baseUrl');
  }

  // Register new user
  Future<Map<String, dynamic>> registerUser({
    required String firstName,
    required String lastName,
    required String phone,
    required String password,
    required String confirmPassword,
    required BuildContext context,
  }) async {
    logger.i('Attempting to register new user: $phone');
    try {
      logger.d('Sending registration request to: $baseUrl/register_process.php');
      final response = await http.post(
        Uri.parse('$baseUrl/register_process.php'),
        body: {
          'first_name': firstName,
          'last_name': lastName,
          'phone': phone,
          'password': password,
          'confirm_password': confirmPassword,
        },
      ).timeout(const Duration(seconds: 30));

      final responseBody = response.body.trim();
      logger.d('Registration response: $responseBody');
      
      if (responseBody.contains('Username_already_exists')) {
        ErrorHandler.handleError(context, 'Username_already_exists', 
          customMessage: 'رقم الهاتف مسجل بالفعل');
        return {'success': false, 'message': 'رقم الهاتف مسجل بالفعل'};
      } else if (responseBody.contains('password_error')) {
        logger.w('Registration failed: Password too short');
        return {'success': false, 'message': 'كلمة المرور يجب أن تكون على الأقل 6 أحرف'};
      } else if (responseBody.contains('Passwords do not match')) {
        logger.w('Registration failed: Passwords do not match');
        return {'success': false, 'message': 'كلمات المرور غير متطابقة'};
      } else if (responseBody.contains('User registration successful')) {
        logger.i('Registration successful for user: $phone');
        return {'success': true, 'message': 'تم التسجيل بنجاح'};
      } else {
        logger.w('Registration failed with unknown response: $responseBody');
        return {'success': false, 'message': 'حدث خطأ أثناء التسجيل'};
      }
    } catch (e) {
      ErrorHandler.handleError(context, e);
      return {'success': false, 'message': 'حدث خطأ أثناء التسجيل'};
    }
  }

  // Login user
  Future<Map<String, dynamic>> loginUser({
    required String phone,
    required String password,
    required BuildContext context,
  }) async {
    logger.i('Attempting to login user: $phone');
    try {
      // New API endpoint using GET with query parameters
      final uri = Uri.parse('$baseUrl/?user=$phone&key=$password');
      logger.d('Sending login request to: $uri');
      final response = await http.get(uri)
          .timeout(const Duration(seconds: 30));
      
      logger.d('Login response status code: ${response.statusCode}');

      // Parse the JSON response
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      logger.d('Login response data: ${responseData.toString()}');

      // Handle response based on status code
      switch (response.statusCode) {
        case 200:
          if (responseData['status'] == 'success') {
            logger.i('Login successful for user: $phone');
            // Create a user object on successful login
            final user = User(
              firstName: '',
              lastName: '',
              phone: phone,
              token: '',
            );
            return {
              'success': true,
              'user': user,
              'message': 'تم تسجيل الدخول بنجاح',
              'redirect': responseData['redirect']
            };
          } else {
            logger.w('Unexpected response from server: ${responseData['status']}');
            return {'success': false, 'message': 'استجابة غير متوقعة من الخادم'};
          }
        case 401:
          logger.w('Login failed: Wrong password for user $phone');
          return {'success': false, 'message': 'كلمة المرور غير صحيحة'};
        case 404:
          logger.w('Login failed: User not found - $phone');
          return {'success': false, 'message': 'المستخدم غير موجود'};
        default:
          logger.w('Login failed with status code ${response.statusCode}: ${responseData['message'] ?? 'Unknown error'}');
          return {'success': false, 'message': responseData['message'] ?? 'حدث خطأ في الاتصال بالخادم'};
      }
    } catch (e) {
      ErrorHandler.handleError(context, e);
      return {'success': false, 'message': 'حدث خطأ أثناء تسجيل الدخول'};
    }
  }
}
