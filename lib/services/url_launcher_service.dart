import 'package:url_launcher/url_launcher.dart';

class UrlLauncherService {
  static Future<bool> openTelegram(String inviteCode) async {
    try {
      // Try to open in Telegram app first
      final telegramUri = Uri.parse('tg://join?invite=$inviteCode');
      if (await canLaunchUrl(telegramUri)) {
        return await launchUrl(telegramUri);
      }
      
      // Fallback to web URL
      final webUrl = Uri.parse('https://t.me/+$inviteCode');
      return await launchUrl(
        webUrl,
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      return false;
    }
  }

  static Future<bool> openUrl(String url, {LaunchMode? mode}) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        return await launchUrl(
          uri,
          mode: mode ?? LaunchMode.platformDefault,
        );
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> openEmail(String email, {String? subject, String? body}) async {
    try {
      final Uri emailUri = Uri(
        scheme: 'mailto',
        path: email,
        queryParameters: {
          if (subject != null) 'subject': subject,
          if (body != null) 'body': body,
        },
      );
      
      if (await canLaunchUrl(emailUri)) {
        return await launchUrl(emailUri);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> openPhone(String phoneNumber) async {
    try {
      final Uri phoneUri = Uri(
        scheme: 'tel',
        path: phoneNumber,
      );
      
      if (await canLaunchUrl(phoneUri)) {
        return await launchUrl(phoneUri);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> openWhatsApp(String phoneNumber, {String? message}) async {
    try {
      String url = 'https://wa.me/$phoneNumber';
      if (message != null) {
        url += '?text=${Uri.encodeComponent(message)}';
      }
      
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        return await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}