import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:logger/logger.dart';

class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  final Logger _logger = Logger();
  final DefaultCacheManager _cacheManager = DefaultCacheManager();

  factory PerformanceService() {
    return _instance;
  }

  PerformanceService._internal();

  // Initialize performance monitoring
  Future<void> initialize() async {
    try {
      _logger.i('Performance monitoring initialized');
    } catch (e, stackTrace) {
      _logger.e('Error initializing performance monitoring',
          error: e, stackTrace: stackTrace);
    }
  }

  // Optimize and cache images
  Future<String?> getOptimizedImage(String imageUrl) async {
    try {
      final fileInfo = await _cacheManager.getFileFromCache(imageUrl);
      if (fileInfo != null) {
        return fileInfo.file.path;
      }

      final file = await _cacheManager.downloadFile(imageUrl);
      final result = await FlutterImageCompress.compressWithFile(
        file.file.path,
        minWidth: 1024,
        minHeight: 1024,
        quality: 85,
      );

      if (result != null) {
        await _cacheManager.putFile(
          imageUrl,
          result,
          maxAge: const Duration(days: 7),
        );
        return file.file.path;
      }
      return null;
    } catch (e, stackTrace) {
      _logger.e('Error optimizing image', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  // Clear cache
  Future<void> clearCache() async {
    try {
      await _cacheManager.emptyCache();
      _logger.i('Cache cleared successfully');
    } catch (e, stackTrace) {
      _logger.e('Error clearing cache', error: e, stackTrace: stackTrace);
    }
  }

  // Memory management
  void disposeResources() {
    _cacheManager.dispose();
    _logger.i('Resources disposed');
  }

  // Performance monitoring
  Future<void> reportPerformanceIssue(String message,
      {Map<String, dynamic>? extra}) async {
    try {
      _logger.w(message, error: extra);  // Add the extra info as error parameter
    } catch (e, stackTrace) {
      _logger.e('Error reporting performance issue',
          error: e, stackTrace: stackTrace);
    }
  }
}
