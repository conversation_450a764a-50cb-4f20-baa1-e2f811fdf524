import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'logger_service.dart';

enum NetworkStatus { online, offline }

class ConnectivityService {
  // Create our public controller
  StreamController<NetworkStatus> connectionStatusController = StreamController<NetworkStatus>.broadcast();

  ConnectivityService() {
    logger.i('ConnectivityService initialized');
    // Subscribe to the connectivity changed stream
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      final status = _getNetworkStatus(result);
      logger.d('Connectivity changed: $result -> $status');
      connectionStatusController.add(status);
    });
  }

  // Convert from the third party enum to our own enum
  NetworkStatus _getNetworkStatus(ConnectivityResult result) {
    final status = result == ConnectivityResult.none ? NetworkStatus.offline : NetworkStatus.online;
    logger.v('Converting connectivity result: $result to $status');
    return status;
  }

  // Check current connectivity status
  Future<NetworkStatus> checkConnectivity() async {
    logger.d('Checking current connectivity status');
    try {
      ConnectivityResult result = await Connectivity().checkConnectivity();
      final status = _getNetworkStatus(result);
      logger.d('Current connectivity status: $status (from $result)');
      return status;
    } catch (e) {
      logger.e('Error checking connectivity', e, StackTrace.current);
      // Default to online to avoid false negatives
      return NetworkStatus.online;
    }
  }

  void dispose() {
    logger.d('Disposing ConnectivityService');
    connectionStatusController.close();
  }
}
