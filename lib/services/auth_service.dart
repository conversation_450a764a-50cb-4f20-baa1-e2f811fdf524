import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import 'logger_service.dart';

class AuthService {
  static const String USER_KEY = 'user_data';
  static const String REDIRECT_URL_KEY = 'redirect_url';

  // استخدام متغير لتخزين مرجع SharedPreferences لتجنب إعادة الحصول عليه في كل مرة
  SharedPreferences? _prefs;

  AuthService() {
    logger.i('AuthService initialized');
    // تهيئة SharedPreferences مسبقًا لتسريع العمليات اللاحقة
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    _prefs = await SharedPreferences.getInstance();
    logger.d('SharedPreferences initialized');
  }

  // Save user data to SharedPreferences
  Future<bool> saveUser(User user) async {
    logger.d('Saving user data for: ${user.phone}');
    try {
      // استخدام المتغير المخزن مسبقًا
      _prefs ??= await SharedPreferences.getInstance();
      final result =
          await _prefs!.setString(USER_KEY, jsonEncode(user.toJson()));
      if (result) {
        logger.i('User data saved successfully: ${user.phone}');
      } else {
        logger.w('Failed to save user data for: ${user.phone}');
      }
      return result;
    } catch (e) {
      logger.e('Error saving user data', e, StackTrace.current);
      return false;
    }
  }

  // Get user data from SharedPreferences
  Future<User?> getUser() async {
    logger.d('Retrieving user data from SharedPreferences');
    try {
      // استخدام المشغل ??= لتعيين القيمة إذا كانت null
      _prefs ??= await SharedPreferences.getInstance();

      final userData = _prefs!.getString(USER_KEY);

      if (userData != null) {
        final user = User.fromJson(jsonDecode(userData));
        logger.i('User data retrieved successfully: ${user.phone}');
        return user;
      } else {
        logger.d('No user data found in SharedPreferences');
        return null;
      }
    } catch (e) {
      logger.e('Error retrieving user data', e, StackTrace.current);
      return null;
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    logger.d('Checking if user is logged in');
    try {
      final user = await getUser();
      final isLoggedIn = user != null;
      logger.d('User logged in status: $isLoggedIn');
      return isLoggedIn;
    } catch (e) {
      logger.e('Error checking login status', e, StackTrace.current);
      return false;
    }
  }

  // Save redirect URL
  Future<bool> saveRedirectUrl(String url) async {
    logger.d('Saving redirect URL: $url');
    try {
      // استخدام المتغير المخزن مسبقًا
      _prefs ??= await SharedPreferences.getInstance();
      final result = await _prefs!.setString(REDIRECT_URL_KEY, url);
      if (result) {
        logger.i('Redirect URL saved successfully');
      } else {
        logger.w('Failed to save redirect URL');
      }
      return result;
    } catch (e) {
      logger.e('Error saving redirect URL', e, StackTrace.current);
      return false;
    }
  }

  // Get redirect URL
  Future<String?> getRedirectUrl() async {
    logger.d('Retrieving redirect URL from SharedPreferences');
    try {
      // استخدام المشغل ??= لتعيين القيمة إذا كانت null
      _prefs ??= await SharedPreferences.getInstance();

      final url = _prefs!.getString(REDIRECT_URL_KEY);
      if (url != null) {
        logger.i('Redirect URL retrieved successfully');
      } else {
        logger.d('No redirect URL found in SharedPreferences');
      }
      return url;
    } catch (e) {
      logger.e('Error retrieving redirect URL', e, StackTrace.current);
      return null;
    }
  }

  // Clear both user data and redirect URL (logout)
  Future<bool> logout() async {
    logger.i('Logging out user and clearing redirect URL');
    try {
      // استخدام المتغير المخزن مسبقًا
      _prefs ??= await SharedPreferences.getInstance();
      final results = await Future.wait([
        _prefs!.remove(USER_KEY),
        _prefs!.remove(REDIRECT_URL_KEY),
      ]);
      final success = results.every((result) => result);
      if (success) {
        logger.i('User logged out and redirect URL cleared successfully');
      } else {
        logger.w('Failed to clear some data during logout');
      }
      return success;
    } catch (e) {
      logger.e('Error during logout process', e, StackTrace.current);
      return false;
    }
  }
}
