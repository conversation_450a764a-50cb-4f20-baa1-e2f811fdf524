class User {
  final String firstName;
  final String lastName;
  final String phone;
  final String? token;

  User({
    required this.firstName,
    required this.lastName,
    required this.phone,
    this.token,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      phone: json['phone'] ?? '',
      token: json['token'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'token': token,
    };
  }
}
