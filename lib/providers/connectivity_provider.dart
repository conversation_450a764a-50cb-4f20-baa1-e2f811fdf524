import 'dart:async';
import 'package:flutter/material.dart';
import '../services/connectivity_service.dart';
import '../services/logger_service.dart';

class ConnectivityProvider with ChangeNotifier {
  final ConnectivityService _connectivityService = ConnectivityService();
  late StreamSubscription<NetworkStatus> _subscription;
  NetworkStatus _networkStatus = NetworkStatus.online;

  NetworkStatus get networkStatus => _networkStatus;
  bool get isOnline => _networkStatus == NetworkStatus.online;

  ConnectivityProvider() {
    logger.i('ConnectivityProvider initialized');
    _init();
  }

  Future<void> _init() async {
    logger.d('Initializing network connectivity monitoring');
    _networkStatus = await _connectivityService.checkConnectivity();
    logger.i('Initial network status: $_networkStatus');
    
    _subscription = _connectivityService.connectionStatusController.stream.listen((status) {
      logger.i('Network status changed: $status');
      _networkStatus = status;
      notifyListeners();
    });
    
    notifyListeners();
  }

  @override
  void dispose() {
    logger.d('Disposing ConnectivityProvider');
    _subscription.cancel();
    _connectivityService.dispose();
    super.dispose();
  }
}
