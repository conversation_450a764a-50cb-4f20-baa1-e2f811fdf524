import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/logger_service.dart';

enum AuthStatus {
  uninitialized,
  authenticated,
  unauthenticated,
}

class AuthProvider with ChangeNotifier {
  AuthStatus _status = AuthStatus.uninitialized;
  User? _user;
  String? _errorMessage;
  bool _loading = false;
  String? _redirectUrl;

  final ApiService _apiService = ApiService();
  final AuthService _authService = AuthService();

  AuthStatus get status => _status;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get loading => _loading;
  String? get redirectUrl => _redirectUrl;

  AuthProvider() {
    logger.i('AuthProvider initialized');
    // Check if the user is already logged in
    _checkCurrentUser();
  }

  Future<void> _checkCurrentUser() async {
    logger.d('Checking current user authentication status');
    _loading = true;
    notifyListeners();

    try {
      // تحسين الأداء باستخدام Future.wait لتنفيذ العمليات بالتوازي
      final results = await Future.wait(
          [_authService.getUser(), _authService.getRedirectUrl()]);

      final user = results[0] as User?;
      final redirectUrl = results[1] as String?;

      if (user != null && redirectUrl != null) {
        _user = user;
        _redirectUrl = redirectUrl;
        _status = AuthStatus.authenticated;
        logger.i(
            'User authenticated: ${user.phone} with redirect URL: $redirectUrl');
      } else {
        _status = AuthStatus.unauthenticated;
        logger.i('No authenticated user or redirect URL found');
      }
    } catch (e) {
      _status = AuthStatus.unauthenticated;
      _errorMessage = 'حدث خطأ: $e';
      logger.e('Error checking current user', e, StackTrace.current);
    }

    _loading = false;
    notifyListeners();
    logger.d('Completed checking user authentication. Status: $_status');
  }

  Future<String> login(String phone, String password,
      {required BuildContext context}) async {
    logger.i('Attempting login for user: $phone');
    _loading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      logger.d('Calling API for login');
      final result = await _apiService.loginUser(
        phone: phone,
        password: password,
        context: context,
      );

      if (result['success']) {
        logger.i('Login successful for user: $phone');
        if (result.containsKey('user')) {
          _user = result['user'];
          await _authService.saveUser(_user!);
          logger.d('User details saved from API response');
        } else {
          // Create a simple user object if the API didn't return user details
          logger
              .d('API did not return user details, creating basic user object');
          _user = User(
            firstName: '',
            lastName: '',
            phone: phone,
            token: '',
          );
          await _authService.saveUser(_user!);
        }

        // Save redirect URL
        if (result['redirect'] != null && result['redirect'].isNotEmpty) {
          _redirectUrl = result['redirect'];
          await _authService.saveRedirectUrl(_redirectUrl!);
          logger.d('Redirect URL saved: $_redirectUrl');
        }

        _status = AuthStatus.authenticated;
        _loading = false;
        notifyListeners();
        return result['redirect'];
      } else {
        logger.w('Login failed: ${result['message']}');
        _errorMessage = result['message'];
        _status = AuthStatus.unauthenticated;
        _loading = false;
        notifyListeners();
        return "";
      }
    } catch (e) {
      logger.e('Error during login process', e, StackTrace.current);
      _errorMessage = 'حدث خطأ أثناء تسجيل الدخول: $e';
      _status = AuthStatus.unauthenticated;
      _loading = false;
      notifyListeners();
      return "";
    }
  }

  Future<String> register({
    required String firstName,
    required String lastName,
    required String phone,
    required String password,
    required String confirmPassword,
    required BuildContext context,
  }) async {
    _loading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await _apiService.registerUser(
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        password: password,
        confirmPassword: confirmPassword,
        context: context,
      );

      if (result['success']) {
        // Auto login after successful registration
        return await login(phone, password, context: context);
      } else {
        _errorMessage = result['message'];
        _status = AuthStatus.unauthenticated;
        _loading = false;
        notifyListeners();
        return "";
      }
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء التسجيل: $e';
      _status = AuthStatus.unauthenticated;
      _loading = false;
      notifyListeners();
      return "";
    }
  }

  Future<void> logout() async {
    _loading = true;
    notifyListeners();

    await _authService.logout();
    _user = null;
    _redirectUrl = null;
    _status = AuthStatus.unauthenticated;

    _loading = false;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
