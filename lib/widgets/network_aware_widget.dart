import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/connectivity_provider.dart';
import '../screens/offline_screen.dart';

/// A widget that checks for network connectivity and shows
/// the OfflineScreen when there's no connectivity.
class NetworkAwareWidget extends StatelessWidget {
  final Widget onlineChild;
  final Widget? offlineChild;

  const NetworkAwareWidget({
    super.key,
    required this.onlineChild,
    this.offlineChild,
  });

  @override
  Widget build(BuildContext context) {
    final connectivityProvider = Provider.of<ConnectivityProvider>(context);
    
    if (connectivityProvider.isOnline) {
      return onlineChild;
    } else {
      return offlineChild ?? const OfflineScreen();
    }
  }
}
