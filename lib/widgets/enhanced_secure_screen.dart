import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:screen_protector/screen_protector.dart' as screen_protector;
import 'dart:io' show Platform;
import 'dart:async';
import 'dart:math';
import '../utils/screenshot_prevention.dart';

/// ويدجت محسن لمنع الاسكرين شوت والاسكرين ريكورد مع تركيز خاص على iOS
/// تم تحسينه باستخدام مكتبة screen_protector
class EnhancedSecureScreen extends StatefulWidget {
  final Widget child;
  final bool enforceProtection;

  const EnhancedSecureScreen({
    super.key,
    required this.child,
    this.enforceProtection = true,
  });

  @override
  State<EnhancedSecureScreen> createState() => _EnhancedSecureScreenState();
}

class _EnhancedSecureScreenState extends State<EnhancedSecureScreen>
    with WidgetsBindingObserver {
  final _noScreenshot = NoScreenshot.instance;
  final _screenshotPrevention = ScreenshotPrevention();
  Timer? _protectionCheckTimer;
  Timer? _continuousProtectionTimer;
  static const MethodChannel _channel =
      MethodChannel('com.app.noorbiology/security');
  bool _isScreenBeingCaptured = false;
  bool _isScreenBeingRecorded = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setupMethodChannel();
    _enableProtection();

    // إضافة مستمع لاكتشاف تسجيل الشاشة
    _screenshotPrevention
        .addRecordingStatusListener(_handleRecordingStatusChanged);

    // إضافة مستمع لاكتشاف التقاط الشاشة
    _screenshotPrevention
        .addScreenshotDetectedListener(_handleScreenshotDetected);

    // إضافة فحص دوري للتأكد من تفعيل الحماية (خاصة لـ iOS)
    if (Platform.isIOS && widget.enforceProtection) {
      // تقليل الفاصل الزمني إلى 1 ثانية لتحسين الاستجابة
      _protectionCheckTimer = Timer.periodic(const Duration(seconds: 1), (_) {
        _enableProtection();
      });

      // إضافة حماية مستمرة بتحديث الواجهة بشكل متكرر
      _continuousProtectionTimer =
          Timer.periodic(const Duration(milliseconds: 500), (_) {
        if (mounted && Platform.isIOS) {
          // تحديث الواجهة بشكل طفيف لمنع التقاط صورة واضحة
          setState(() {
            // تحديث طفيف للواجهة
          });
        }
      });
    }
  }

  // إعداد قناة الاتصال مع الكود الأصلي
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      if (call.method == 'screenCaptureDetected') {
        _isScreenBeingCaptured = true;
        _handleScreenCapture();
        return null;
      }
      return null;
    });
  }

  // معالجة تغيير حالة تسجيل الشاشة
  void _handleRecordingStatusChanged(bool isRecording) {
    if (mounted && widget.enforceProtection) {
      setState(() {
        _isScreenBeingRecorded = isRecording;
      });

      if (isRecording) {
        _intensifyProtection();
      }
    }
  }

  // معالجة اكتشاف التقاط الشاشة
  void _handleScreenshotDetected() {
    if (mounted && widget.enforceProtection) {
      setState(() {
        _isScreenBeingCaptured = true;
      });

      _handleScreenCapture();
    }
  }

  // معالجة حدث التقاط الشاشة
  void _handleScreenCapture() {
    if (mounted && widget.enforceProtection) {
      // تطبيق إجراءات إضافية عند اكتشاف التقاط الشاشة
      setState(() {
        // تحديث الواجهة لتطبيق الحماية
      });

      // تكثيف الحماية مؤقتًا
      _intensifyProtection();
    }
  }

  // تكثيف الحماية بشكل مؤقت
  void _intensifyProtection() {
    // إنشاء مؤقت لتكثيف الحماية لمدة 5 ثوان
    Timer(const Duration(seconds: 5), () {
      if (mounted) {
        _isScreenBeingCaptured = false;
        setState(() {});
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // تعزيز الحماية لجميع الأنظمة مع تركيز خاص على iOS
    switch (state) {
      case AppLifecycleState.resumed:
        _enableProtection();
        // إعادة تشغيل المؤقتات للفحص الدوري على iOS
        if (Platform.isIOS && widget.enforceProtection) {
          if (_protectionCheckTimer == null) {
            _protectionCheckTimer =
                Timer.periodic(const Duration(seconds: 1), (_) {
              _enableProtection();
            });
          }

          if (_continuousProtectionTimer == null) {
            _continuousProtectionTimer =
                Timer.periodic(const Duration(milliseconds: 500), (_) {
              if (mounted) {
                setState(() {
                  // تحديث طفيف للواجهة
                });
              }
            });
          }
        }
        break;
      case AppLifecycleState.paused:
        // ضمان الحماية عند وضع التطبيق في الخلفية
        if (widget.enforceProtection) {
          _noScreenshot.screenshotOff();

          // إيقاف المؤقتات عند وضع التطبيق في الخلفية
          _protectionCheckTimer?.cancel();
          _protectionCheckTimer = null;

          _continuousProtectionTimer?.cancel();
          _continuousProtectionTimer = null;
        }
        break;
      case AppLifecycleState.inactive:
        // تعزيز الحماية عندما يكون التطبيق غير نشط (مثل عند فتح مركز التحكم)
        if (widget.enforceProtection && Platform.isIOS) {
          _noScreenshot.screenshotOff();
          _applyAdditionalIOSProtection();

          // تكثيف الحماية في هذه الحالة
          if (mounted) {
            setState(() {
              // تحديث الواجهة لتطبيق الحماية
            });
          }
        }
        break;
      case AppLifecycleState.detached:
        // تنظيف الموارد عند انفصال التطبيق
        _cleanupResources();
        break;
      default:
        break;
    }
  }

  Future<void> _enableProtection() async {
    if (widget.enforceProtection) {
      try {
        // استخدام no_screenshot كطبقة أساسية للحماية
        await _noScreenshot.screenshotOff();

        // محاولة استخدام screen_protector إذا كانت متاحة
        try {
          if (Platform.isAndroid) {
            // تفعيل الحماية على Android
            await screen_protector.ScreenProtector.protectDataLeakageOn();
          } else if (Platform.isIOS) {
            // منع التقاط الشاشة على iOS
            await screen_protector.ScreenProtector.preventScreenshotOn();

            // حماية من تسرب البيانات عند وضع التطبيق في الخلفية
            await screen_protector.ScreenProtector.protectDataLeakageWithBlur();

            // فحص حالة تسجيل الشاشة
            try {
              final isRecording =
                  await screen_protector.ScreenProtector.isRecording();
              if (isRecording != _isScreenBeingRecorded) {
                setState(() {
                  _isScreenBeingRecorded = isRecording;
                });

                if (isRecording) {
                  _intensifyProtection();
                }
              }
            } catch (e) {
              debugPrint('Recording check error: $e');
            }
          }
        } catch (e) {
          debugPrint('Screen protector method error: $e');
          // تجاهل الأخطاء هنا لأن no_screenshot تعمل كطبقة أساسية
        }

        // تعزيز إضافي خاص بـ iOS
        if (Platform.isIOS) {
          await _applyAdditionalIOSProtection();

          // استدعاء الكود الأصلي لتعزيز الحماية
          try {
            await _channel.invokeMethod('enableScreenProtection');
          } catch (e) {
            // تجاهل الأخطاء هنا
            debugPrint('Native protection method error: $e');
          }
        }
      } catch (e) {
        debugPrint('Screenshot protection error: $e');
        // محاولة استخدام طريقة بديلة في حالة فشل الطريقة الأساسية
        _applyFallbackProtection();
      }
    }
  }

  // طريقة إضافية لتعزيز الحماية على iOS
  Future<void> _applyAdditionalIOSProtection() async {
    if (Platform.isIOS) {
      try {
        // تطبيق حماية إضافية باستخدام no_screenshot مرة أخرى للتأكيد
        await _noScreenshot.screenshotOff();

        // استخدام تقنيات إضافية لمنع التقاط الشاشة
        if (_isScreenBeingCaptured) {
          // تطبيق حماية مكثفة عند اكتشاف محاولة التقاط
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                // تحديث الواجهة بشكل مكثف
              });
            }
          });
        }
      } catch (e) {
        // تجاهل الأخطاء هنا لأن هذه طبقة إضافية
        debugPrint('Additional iOS protection error: $e');
      }
    }
  }

  // طريقة بديلة للحماية في حالة فشل الطريقة الأساسية
  void _applyFallbackProtection() {
    if (Platform.isIOS) {
      try {
        // استخدام طريقة بديلة لمنع الاسكرين شوت على iOS
        // هذه الطريقة تعتمد على تحديث الواجهة بشكل متكرر لمنع التقاط صورة واضحة
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              // تحديث الواجهة لتطبيق الحماية
            });
          }
        });

        // إضافة مؤقت للتحديث المستمر إذا لم يكن موجودًا
        if (_continuousProtectionTimer == null) {
          _continuousProtectionTimer =
              Timer.periodic(const Duration(milliseconds: 500), (_) {
            if (mounted) {
              setState(() {
                // تحديث طفيف للواجهة
              });
            }
          });
        }
      } catch (e) {
        debugPrint('Fallback protection error: $e');
      }
    }
  }

  // تنظيف الموارد
  void _cleanupResources() {
    _protectionCheckTimer?.cancel();
    _protectionCheckTimer = null;

    _continuousProtectionTimer?.cancel();
    _continuousProtectionTimer = null;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // تنظيف الموارد
    _cleanupResources();

    // إزالة المستمعين
    _screenshotPrevention
        .removeScreenshotDetectedListener(_handleScreenshotDetected);
    _screenshotPrevention
        .removeRecordingStatusListener(_handleRecordingStatusChanged);

    // إيقاف الحماية فقط إذا كان التطبيق سيتم إغلاقه
    // هذا يضمن استمرار الحماية عند الانتقال بين الشاشات
    if (!widget.enforceProtection) {
      _noScreenshot.screenshotOn();

      // محاولة استخدام screen_protector إذا كانت متاحة
      try {
        if (Platform.isAndroid) {
          screen_protector.ScreenProtector.protectDataLeakageOff();
        } else if (Platform.isIOS) {
          screen_protector.ScreenProtector.preventScreenshotOff();
          screen_protector.ScreenProtector.protectDataLeakageWithBlurOff();
        }
      } catch (e) {
        debugPrint('Screen protector disable error: $e');
      }
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // إضافة طبقة تحذير عند اكتشاف تسجيل الشاشة
    if (_isScreenBeingRecorded && widget.enforceProtection) {
      return Stack(
        alignment: Alignment.topLeft,
        textDirection: TextDirection.rtl,
        children: [
          widget.child,
          // طبقة تحذير لتسجيل الشاشة
          Positioned.fill(
            child: Material(
              color: Colors.black.withAlpha(230),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.videocam_off,
                      color: Colors.red,
                      size: 60,
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'تم اكتشاف تسجيل الشاشة!',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Text(
                      'تسجيل الشاشة غير مسموح لأسباب أمنية',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    }

    // إضافة طبقة شفافة للحماية إذا كان هناك محاولة التقاط
    if (_isScreenBeingCaptured && widget.enforceProtection) {
      return Stack(
        // تحديد alignment بشكل صريح بدلاً من استخدام القيمة الافتراضية
        alignment: Alignment.topLeft,
        textDirection: TextDirection.rtl, // تحديد اتجاه النص بشكل صريح
        children: [
          widget.child,
          // طبقة شفافة للحماية مع تأثير بصري خفيف
          Positioned.fill(
            child: IgnorePointer(
              child: Container(
                // استخدام لون مع شفافية بدلاً من استخدام AnimatedOpacity
                color: Colors.black
                    .withAlpha(13), // ما يعادل تقريبًا opacity: 0.05
              ),
            ),
          ),
          // إضافة طبقة تشويش إضافية
          Positioned.fill(
            child: IgnorePointer(
              child: ShaderMask(
                shaderCallback: (Rect bounds) {
                  return LinearGradient(
                    colors: [
                      Colors.transparent,
                      Colors.white.withAlpha(5),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ).createShader(bounds);
                },
                blendMode: BlendMode.srcOver,
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
          ),
        ],
      );
    }

    // إضافة طبقة حماية خفية دائمة لمنع التقاط الشاشة
    if (widget.enforceProtection) {
      return RepaintBoundary(
        child: Stack(
          // تحديد alignment بشكل صريح بدلاً من استخدام القيمة الافتراضية
          alignment: Alignment.topLeft,
          textDirection: TextDirection.rtl, // تحديد اتجاه النص بشكل صريح
          children: [
            widget.child,
            // طبقة شفافة للحماية المستمرة
            Positioned.fill(
              child: IgnorePointer(
                child: Container(
                  // استخدام لون شفاف جدًا بدلاً من استخدام Opacity
                  color: Colors.black
                      .withAlpha(3), // ما يعادل تقريبًا opacity: 0.01
                ),
              ),
            ),
            // إضافة طبقة حماية إضافية باستخدام CustomPaint
            Positioned.fill(
              child: IgnorePointer(
                child: CustomPaint(
                  painter: SecurityPatternPainter(),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return widget.child;
  }
}

/// رسام نمط أمان مخصص
class SecurityPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // رسم نمط أمان خفي جدًا
    final paint = Paint()
      ..color = Colors.white.withAlpha(1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.1;

    // رسم شبكة من الخطوط المتقاطعة بشكل عشوائي
    final random = Random();
    for (int i = 0; i < 50; i++) {
      final x1 = random.nextDouble() * size.width;
      final y1 = random.nextDouble() * size.height;
      final x2 = random.nextDouble() * size.width;
      final y2 = random.nextDouble() * size.height;
      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
