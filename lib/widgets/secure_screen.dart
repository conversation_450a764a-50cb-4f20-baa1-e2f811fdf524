import 'package:flutter/material.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'dart:io' show Platform;

class SecureScreen extends StatefulWidget {
  final Widget child;
  final bool enforceProtection;

  const SecureScreen({
    super.key,
    required this.child,
    this.enforceProtection = true,
  });

  @override
  State<SecureScreen> createState() => _SecureScreenState();
}

class _SecureScreenState extends State<SecureScreen> with WidgetsBindingObserver {
  final _noScreenshot = NoScreenshot.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _enableProtection();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (Platform.isIOS) {
      switch (state) {
        case AppLifecycleState.resumed:
          _enableProtection();
          break;
        case AppLifecycleState.paused:
          // Ensure protection when app goes to background
          if (widget.enforceProtection) {
            _noScreenshot.screenshotOff();
          }
          break;
        default:
          break;
      }
    }
  }

  Future<void> _enableProtection() async {
    if (widget.enforceProtection) {
      try {
        // Use the no_screenshot plugin to prevent screenshots
        // The plugin handles both Android and iOS internally
        await _noScreenshot.screenshotOff();
      } catch (e) {
        debugPrint('Screenshot protection error: $e');
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    if (widget.enforceProtection) {
      _noScreenshot.screenshotOn();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => widget.child;
}
