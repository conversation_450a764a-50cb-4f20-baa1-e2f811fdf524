import 'package:flutter/material.dart';

import 'package:webview_flutter/webview_flutter.dart';
import '../utils/constants.dart';
import '../services/performance_service.dart';
import '../utils/webview_lifecycle_manager.dart';
import '../widgets/enhanced_secure_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with WidgetsBindingObserver {
  WebViewController? _controller;
  final _performanceService = PerformanceService();
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  // استخدام UniqueKey لتجنب مشكلة إعادة إنشاء WebView بنفس المعرف
  late final Key _webViewKey;

  @override
  void initState() {
    super.initState();
    // إنشاء مفتاح فريد لكل مرة يتم فيها إنشاء الويدجت
    _webViewKey = UniqueKey();
    WidgetsBinding.instance.addObserver(this);
    _initWebView();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        // Only reload if the app was in background for a significant time
        // This prevents reloads when just pulling down the notification panel
        if (WebViewLifecycleManager.shouldReloadOnResume()) {
          _reloadWebView();
        }
        break;
      case AppLifecycleState.inactive:
        // App is inactive but still visible (like when showing notification panel)
        // Track this state to prevent unnecessary reloads
        WebViewLifecycleManager.onAppInactive();
        break;
      case AppLifecycleState.paused:
        // App is no longer visible
        WebViewLifecycleManager.onAppPaused();
        break;
      default:
        break;
    }
  }

  void _initWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
                _errorMessage = null;
              });
            }
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() => _isLoading = false);
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = 'حدث خطأ: ${error.description}';
              });
            }
            _performanceService.reportPerformanceIssue(
              'WebView error in Settings: ${error.description}',
              extra: {'url': 'settings', 'errorCode': error.errorCode},
            );
          },
        ),
      )
      ..loadRequest(Uri.parse('https://noorbiology.com/dashboard/settings/'));
  }

  void _reloadWebView() {
    _controller?.reload();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller = null;
    _performanceService.disposeResources();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedSecureScreen(
        enforceProtection: true,
        child: Scaffold(
          appBar: AppBar(
            title: const Text('الإعدادات'),
            centerTitle: true,
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _reloadWebView,
              ),
            ],
          ),
          body: Stack(
            children: [
              if (_controller != null)
                WebViewWidget(
                  key: _webViewKey,
                  controller: _controller!,
                ),
              if (_isLoading)
                const Center(
                  child: CircularProgressIndicator(
                    color: AppColors.primary,
                  ),
                ),
              if (_hasError)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage ?? 'حدث خطأ غير معروف',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _reloadWebView,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ));
  }
}
