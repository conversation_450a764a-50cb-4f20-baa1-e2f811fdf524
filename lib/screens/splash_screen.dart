import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/connectivity_provider.dart';
import '../services/logger_service.dart';
import '../utils/constants.dart';
import '../widgets/enhanced_secure_screen.dart';
import 'login_screen.dart';
import 'home_screen.dart';
import 'offline_screen.dart';
import 'security_test_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    logger.i('Splash screen initialized');
    _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    logger.d('Starting splash screen navigation with minimal delay');
    // تقليل التأخير إلى نصف ثانية فقط لإظهار الشاشة بشكل مناسب
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) {
      logger.d('Widget no longer mounted, skipping navigation');
      return;
    }

    logger.d('Checking connectivity and authentication status');
    final connectivityProvider =
        Provider.of<ConnectivityProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!connectivityProvider.isOnline) {
      logger.i('Device is offline, navigating to offline screen');
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const OfflineScreen()),
      );
      return;
    }

    // الانتظار للتحقق من حالة المصادقة بشكل أسرع
    if (authProvider.status == AuthStatus.uninitialized) {
      logger.d('Auth provider still initializing, waiting briefly');
      // تقليل وقت الانتظار إلى 300 مللي ثانية فقط
      await Future.delayed(const Duration(milliseconds: 300));
    }

    if (!mounted) return;

    logger.d('Current auth status: ${authProvider.status}');
    if (authProvider.status == AuthStatus.authenticated) {
      logger.i(
          'User is authenticated, navigating to home screen with cached redirect URL');
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (_) => HomeScreen(
            webUrl: authProvider.redirectUrl ?? "",
          ),
        ),
      );
    } else {
      logger.i('User is not authenticated, navigating to login screen');
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const LoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedSecureScreen(
        enforceProtection: true,
        child: Scaffold(
          backgroundColor: AppColors.backgroundLight,
          body: Stack(
            children: [
              // Background leaves image
              Positioned.fill(
                child: Opacity(
                  opacity: 0.3,
                  child: Image.asset(
                    'assets/images/background_leaves.png',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(color: AppColors.backgroundLight);
                    },
                  ),
                ),
              ),
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo image
                    Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(100),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(25),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(10),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(90),
                        child: Image.asset(
                          'assets/images/logo.png',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.science,
                              size: 100,
                              color: AppColors.primary,
                            );
                          },
                        ),
                      ),
                    ),
                    verticalSpaceLarge,
                    const Text(
                      'منصة الاستاذه نور محمد',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                        shadows: [
                          Shadow(
                            blurRadius: 10.0,
                            color: Colors.black12,
                            offset: Offset(2.0, 2.0),
                          ),
                        ],
                      ),
                    ),
                    verticalSpaceMedium,
                    const SizedBox(height: 20),
                    const CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                    const SizedBox(height: 20),
                    // زر مخفي للوصول إلى شاشة اختبار الحماية (يظهر فقط في وضع التصحيح)
                    TextButton.icon(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => const SecurityTestScreen(),
                          ),
                        );
                      },
                      icon: Icon(Icons.security,
                          size: 16, color: Colors.grey.withAlpha(128)),
                      label: Text('اختبار الحماية',
                          style: TextStyle(
                              fontSize: 12, color: Colors.grey.withAlpha(128))),
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}
