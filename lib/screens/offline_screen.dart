import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import '../providers/connectivity_provider.dart';
import '../utils/constants.dart';
import '../widgets/enhanced_secure_screen.dart';
import 'splash_screen.dart';

class OfflineScreen extends StatelessWidget {
  const OfflineScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final connectivityProvider = Provider.of<ConnectivityProvider>(context);

    // If we are back online, navigate to splash screen
    if (connectivityProvider.isOnline) {
      // Use a post-frame callback to avoid build-time navigation
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const SplashScreen()),
        );
      });
    }

    return EnhancedSecureScreen(
      enforceProtection: true,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Padding(
            padding: screenPadding,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // No internet icon
                const Icon(
                  Icons.wifi_off,
                  size: 80,
                  color: AppColors.primary,
                ),
                verticalSpaceLarge,

                // Title
                const Text(
                  'لا يوجد اتصال بالإنترنت',
                  style: AppTextStyles.headline,
                  textAlign: TextAlign.center,
                ),
                verticalSpaceMedium,

                // Description
                const Text(
                  'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
                  style: AppTextStyles.body,
                  textAlign: TextAlign.center,
                ),
                verticalSpaceLarge,

                // Retry button
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (_) => const SplashScreen()),
                    );
                  },
                  style: primaryButtonStyle,
                  child:
                      const Text('إعادة المحاولة', style: AppTextStyles.button),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
