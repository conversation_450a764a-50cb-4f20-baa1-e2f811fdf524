import 'package:flutter/material.dart';

import 'package:webview_flutter/webview_flutter.dart';
import 'package:shimmer/shimmer.dart';
import '../services/performance_service.dart';
import '../widgets/enhanced_secure_screen.dart';
import '../utils/webview_lifecycle_manager.dart';

class EnrolledCoursesScreen extends StatefulWidget {
  const EnrolledCoursesScreen({super.key});

  @override
  State<EnrolledCoursesScreen> createState() => _EnrolledCoursesScreenState();
}

class _EnrolledCoursesScreenState extends State<EnrolledCoursesScreen>
    with WidgetsBindingObserver {
  WebViewController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  final _performanceService = PerformanceService();
  final String _enrolledCoursesUrl =
      'https://noorbiology.com/dashboard/enrolled-courses/';
  // استخدام UniqueKey لتجنب مشكلة إعادة إنشاء WebView بنفس المعرف
  late final Key _webViewKey;

  @override
  void initState() {
    super.initState();
    // إنشاء مفتاح فريد لكل مرة يتم فيها إنشاء الويدجت
    _webViewKey = UniqueKey();
    WidgetsBinding.instance.addObserver(this);
    // Delay WebView initialization to ensure proper widget lifecycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWebView();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        // Only reload if the app was in background for a significant time
        // This prevents reloads when just pulling down the notification panel
        if (WebViewLifecycleManager.shouldReloadOnResume()) {
          // Reinitialize WebView if needed when app is resumed
          if (_controller == null) {
            _initializeWebView();
          } else {
            _controller?.reload();
          }
        }
        break;
      case AppLifecycleState.inactive:
        // App is inactive but still visible (like when showing notification panel)
        // Track this state to prevent unnecessary reloads
        WebViewLifecycleManager.onAppInactive();
        break;
      case AppLifecycleState.paused:
        // App is no longer visible
        WebViewLifecycleManager.onAppPaused();
        break;
      default:
        break;
    }
  }

  void _initializeWebView() {
    if (_controller != null) return;

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
                _errorMessage = null;
              });
            }
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() => _isLoading = false);
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = 'حدث خطأ: ${error.description}';
              });
            }
            _performanceService.reportPerformanceIssue(
              'WebView error: ${error.description}',
              extra: {'url': _enrolledCoursesUrl, 'errorCode': error.errorCode},
            );
          },
        ),
      );

    if (mounted) {
      setState(() {});
      _controller?.loadRequest(Uri.parse(_enrolledCoursesUrl));
    }
  }

  void _reloadWebView() {
    if (mounted) {
      setState(() {
        _hasError = false;
        _errorMessage = null;
        _isLoading = true;
      });
    }
    _controller?.loadRequest(Uri.parse(_enrolledCoursesUrl));
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // تنظيف موارد WebView بشكل صحيح
    _controller = null;
    _performanceService.disposeResources();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedSecureScreen(
      enforceProtection: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الدورات المسجلة'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _reloadWebView,
            ),
          ],
        ),
        body: Stack(
          children: [
            if (_hasError)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage ?? 'حدث خطأ غير معروف',
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _reloadWebView,
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              )
            else if (_controller != null)
              WebViewWidget(
                key: _webViewKey,
                controller: _controller!,
              ),
            if (_isLoading)
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  color: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
