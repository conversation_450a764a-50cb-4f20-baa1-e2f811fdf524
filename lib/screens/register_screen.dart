import 'package:flutter/material.dart';
import 'package:noorbiology/utils/error_handler.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/constants.dart';
import '../utils/validation.dart';
import '../widgets/enhanced_secure_screen.dart';
import 'home_screen.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _register() async {
    FocusScope.of(context).unfocus();

    if (_formKey.currentState!.validate()) {
      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        final success = await authProvider.register(
          context: context,
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          phone: _phoneController.text.trim(),
          password: _passwordController.text,
          confirmPassword: _confirmPasswordController.text,
        );

        if (success != "" && mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (_) => HomeScreen(webUrl: success)),
            (route) => false,
          );
        }
      } catch (e) {
        ErrorHandler.handleError(context, e);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return EnhancedSecureScreen(
        enforceProtection: true,
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            title: const Text('إنشاء حساب جديد'),
            centerTitle: true,
          ),
          body: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: screenPadding,
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Error message if any
                      if (authProvider.errorMessage != null)
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppColors.error.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            authProvider.errorMessage!,
                            style: const TextStyle(color: AppColors.error),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      if (authProvider.errorMessage != null)
                        verticalSpaceMedium,

                      // First name field
                      TextFormField(
                        controller: _firstNameController,
                        decoration: inputDecoration('الاسم الأول'),
                        keyboardType: TextInputType.name,
                        textInputAction: TextInputAction.next,
                        validator: Validator.validateName,
                        enabled: !authProvider.loading,
                      ),
                      verticalSpaceMedium,

                      // Last name field
                      TextFormField(
                        controller: _lastNameController,
                        decoration: inputDecoration('الاسم الأخير'),
                        keyboardType: TextInputType.name,
                        textInputAction: TextInputAction.next,
                        validator: Validator.validateName,
                        enabled: !authProvider.loading,
                      ),
                      verticalSpaceMedium,

                      // Phone field
                      TextFormField(
                        controller: _phoneController,
                        decoration: inputDecoration('رقم الهاتف'),
                        keyboardType: TextInputType.phone,
                        textDirection: TextDirection.ltr,
                        textInputAction: TextInputAction.next,
                        validator: Validator.validatePhone,
                        enabled: !authProvider.loading,
                      ),
                      verticalSpaceMedium,

                      // Password field
                      TextFormField(
                        controller: _passwordController,
                        decoration: inputDecoration('كلمة المرور').copyWith(
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: Colors.grey,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                        ),
                        obscureText: _obscurePassword,
                        textInputAction: TextInputAction.next,
                        validator: Validator.validatePassword,
                        enabled: !authProvider.loading,
                      ),
                      verticalSpaceMedium,

                      // Confirm password field
                      TextFormField(
                        controller: _confirmPasswordController,
                        decoration:
                            inputDecoration('تأكيد كلمة المرور').copyWith(
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscureConfirmPassword
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: Colors.grey,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscureConfirmPassword =
                                    !_obscureConfirmPassword;
                              });
                            },
                          ),
                        ),
                        obscureText: _obscureConfirmPassword,
                        textInputAction: TextInputAction.done,
                        validator: (value) => Validator.validateConfirmPassword(
                            value, _passwordController.text),
                        enabled: !authProvider.loading,
                      ),
                      verticalSpaceLarge,

                      // Register button
                      ElevatedButton(
                        onPressed: authProvider.loading ? null : _register,
                        style: primaryButtonStyle,
                        child: authProvider.loading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Text('تسجيل', style: AppTextStyles.button),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ));
  }
}
