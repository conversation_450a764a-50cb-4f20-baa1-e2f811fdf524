import 'package:flutter/material.dart';
import '../utils/screen_security_test.dart';
import '../widgets/enhanced_secure_screen.dart';

/// شاشة لاختبار فعالية منع التقاط الشاشة وتسجيلها
class SecurityTestScreen extends StatefulWidget {
  const SecurityTestScreen({super.key});

  @override
  State<SecurityTestScreen> createState() => _SecurityTestScreenState();
}

class _SecurityTestScreenState extends State<SecurityTestScreen> {
  bool _isTestRunning = false;
  int _detectedAttempts = 0;
  
  @override
  void initState() {
    super.initState();
  }
  
  @override
  void dispose() {
    // إيقاف الاختبار عند الخروج من الشاشة
    if (_isTestRunning) {
      ScreenSecurityTest.stopTest(context);
    }
    super.dispose();
  }
  
  // بدء اختبار الحماية
  void _startTest() {
    ScreenSecurityTest.startTest(
      context,
      onDetection: () {
        setState(() {
          _detectedAttempts = ScreenSecurityTest.getDetectedAttempts();
        });
      },
    );
    
    setState(() {
      _isTestRunning = true;
      _detectedAttempts = 0;
    });
  }
  
  // إيقاف اختبار الحماية
  void _stopTest() {
    ScreenSecurityTest.stopTest(context);
    
    setState(() {
      _isTestRunning = false;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return EnhancedSecureScreen(
      enforceProtection: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('اختبار حماية الشاشة'),
          centerTitle: true,
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // صورة توضيحية
                Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.security,
                      size: 100,
                      color: Colors.green,
                    ),
                  ),
                ),
                const SizedBox(height: 30),
                
                // معلومات سرية للاختبار
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.red),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Column(
                    children: [
                      Text(
                        'معلومات سرية للاختبار',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text(
                        'هذه المعلومات سرية ويجب حمايتها من التقاط الشاشة',
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 10),
                      Text(
                        'رمز الاختبار: SEC-TEST-12345',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
                
                // عرض عدد محاولات التقاط الشاشة المكتشفة
                if (_isTestRunning)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          'الاختبار قيد التشغيل',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          'تم اكتشاف $_detectedAttempts محاولة لالتقاط الشاشة',
                          style: TextStyle(
                            color: _detectedAttempts > 0 ? Colors.green : Colors.black,
                            fontWeight: _detectedAttempts > 0 ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 30),
                
                // زر بدء/إيقاف الاختبار
                ElevatedButton(
                  onPressed: _isTestRunning ? _stopTest : _startTest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isTestRunning ? Colors.red : Colors.green,
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                  ),
                  child: Text(
                    _isTestRunning ? 'إيقاف الاختبار' : 'بدء الاختبار',
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
                const SizedBox(height: 20),
                
                // تعليمات الاختبار
                if (!_isTestRunning)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Text(
                      'اضغط على "بدء الاختبار" ثم حاول التقاط صورة للشاشة أو تسجيلها لاختبار فعالية الحماية.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
