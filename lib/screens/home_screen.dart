import 'package:flutter/material.dart';
import 'package:noorbiology/screens/enrolled_courses_screen.dart';
import 'package:noorbiology/utils/constants.dart';
import 'package:noorbiology/utils/error_handler.dart';
import 'package:noorbiology/utils/webview_lifecycle_manager.dart';
import 'package:noorbiology/widgets/enhanced_secure_screen.dart';

import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../providers/auth_provider.dart';
import '../providers/connectivity_provider.dart';
import '../screens/login_screen.dart';
import '../screens/offline_screen.dart';
import '../services/performance_service.dart';
import '../screens/profile_screen.dart';
import '../services/url_launcher_service.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  final String webUrl;

  const HomeScreen({
    super.key,
    required this.webUrl,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  WebViewController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  final _performanceService = PerformanceService();
  // استخدام UniqueKey بدلاً من GlobalKey لتجنب مشكلة إعادة إنشاء WebView بنفس المعرف
  late final Key _webViewKey;

  @override
  void initState() {
    super.initState();
    // إنشاء مفتاح فريد لكل مرة يتم فيها إنشاء الويدجت
    _webViewKey = UniqueKey();
    WidgetsBinding.instance.addObserver(this);
    // Delay WebView initialization to ensure proper widget lifecycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWebView();
    });
  }

  void _initializeWebView() {
    if (_controller != null) return;

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: _handlePageStarted,
          onPageFinished: _handlePageFinished,
          onWebResourceError: _handleWebResourceError,
        ),
      );

    if (mounted) {
      setState(() {
        _loadUrl();
      });
    }
  }

  void _loadUrl() {
    if (_controller == null) return;
    _controller?.loadRequest(Uri.parse(widget.webUrl));
  }

  void _handlePageStarted(String url) {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });
  }

  void _handlePageFinished(String url) {
    if (!mounted) return;
    setState(() => _isLoading = false);
    // تحديث الواجهة لإظهار زر الرجوع للخلف إذا كان متاحًا
    setState(() {});
  }

  void _handleWebResourceError(WebResourceError error) {
    if (!mounted) return;

    setState(() {
      _isLoading = false;
      _hasError = true;
      _errorMessage = 'حدث خطأ: ${error.description}';
    });

    ErrorHandler.handleError(context, error,
        customMessage: 'خطأ في تحميل الصفحة: ${error.description}');

    _performanceService.reportPerformanceIssue(
      'WebView error: ${error.description}',
      extra: {'url': widget.webUrl, 'errorCode': error.errorCode},
    );
  }

  void _reloadWebView() {
    if (_controller == null) {
      _initializeWebView();
    } else {
      _controller?.reload();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        // Only reload if the app was in background for a significant time
        // This prevents reloads when just pulling down the notification panel
        if (WebViewLifecycleManager.shouldReloadOnResume()) {
          // Delay reload to ensure proper view restoration
          Future.delayed(const Duration(milliseconds: 300), _loadUrl);
        }
        break;
      case AppLifecycleState.inactive:
        // App is inactive but still visible (like when showing notification panel)
        // Track this state to prevent unnecessary reloads
        WebViewLifecycleManager.onAppInactive();
        break;
      case AppLifecycleState.paused:
        // App is no longer visible
        WebViewLifecycleManager.onAppPaused();
        break;
      case AppLifecycleState.detached:
        // Handle cleanup if needed
        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // تنظيف موارد WebView بشكل صحيح
    _controller = null;
    _performanceService.disposeResources();
    super.dispose();
  }

  Widget _buildDrawer() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(
              color: AppColors.primary,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      'assets/images/logo.png',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.person,
                          size: 40,
                          color: Colors.white,
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'منصة الاستاذه نور محمد',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text('الرئيسية'),
            onTap: () {
              Navigator.pop(context); // Close drawer
              _reloadWebView();
            },
          ),
          ListTile(
            leading: const Icon(Icons.school),
            title: const Text('الدورات المسجلة'),
            onTap: () {
              Navigator.pop(context); // Close drawer
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnrolledCoursesScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('الملف الشخصي'),
            onTap: () {
              Navigator.pop(context); // Close drawer
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ProfileScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('الإعدادات'),
            onTap: () {
              Navigator.pop(context); // Close drawer
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.support_agent),
            title: const Text('تواصل معنا'),
            onTap: _handleContactUs,
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red),
            ),
            onTap: () async {
              Navigator.pop(context); // Close drawer
              await authProvider.logout();
              if (mounted) {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (_) => const LoginScreen()),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _handleContactUs() async {
    Navigator.pop(context); // Close drawer first

    const inviteCode = '0yiUUpQu3sQ3NDU0';
    final success = await UrlLauncherService.openTelegram(inviteCode);

    if (!success && mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('خطأ'),
          content: const Text('لا يمكن فتح رابط تيليجرام'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('حسناً'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final connectivityProvider = Provider.of<ConnectivityProvider>(context);

    if (!connectivityProvider.isOnline) {
      return const OfflineScreen();
    }

    return EnhancedSecureScreen(
        enforceProtection: true,
        child: Scaffold(
          drawer: _buildDrawer(),
          appBar: AppBar(
            title: const Text('منصة الاستاذه نور محمد'),
            centerTitle: true,
            actions: [
              // زر الرجوع للخلف في WebView
              FutureBuilder<bool>(
                future: _controller?.canGoBack() ?? Future.value(false),
                builder: (context, snapshot) {
                  final canGoBack = snapshot.data == true;
                  return canGoBack
                      ? IconButton(
                          icon: const Icon(Icons.arrow_back),
                          onPressed: () {
                            if (mounted) {
                              _controller?.goBack();
                            }
                          },
                        )
                      : const SizedBox.shrink();
                },
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _reloadWebView,
              ),
            ],
          ),
          body: Stack(
            children: [
              // إنشاء WebView بمفتاح فريد لكل مرة
              if (_controller != null)
                WebViewWidget(
                  key: _webViewKey,
                  controller: _controller!,
                ),
              if (_isLoading)
                const Center(
                  child: CircularProgressIndicator(),
                ),
              if (_hasError)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(_errorMessage ?? 'حدث خطأ غير متوقع'),
                      ElevatedButton(
                        onPressed: _reloadWebView,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ));
  }
}
