import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:noorbiology/widgets/enhanced_secure_screen.dart';
import 'package:provider/provider.dart';
import 'package:screen_protector/screen_protector.dart' as screen_protector;

import 'providers/auth_provider.dart';
import 'providers/connectivity_provider.dart';
import 'screens/splash_screen.dart';
import 'utils/screenshot_prevention.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة منع التقاط الشاشة على مستوى التطبيق
  await ScreenshotPrevention().initialize();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  // تفعيل الحماية من التقاط الشاشة وتسجيلها
  // محاولة استخدام screen_protector إذا كانت متاحة
  try {
    if (Platform.isAndroid) {
      // تفعيل الحماية على Android
      await screen_protector.ScreenProtector.protectDataLeakageOn();
    } else if (Platform.isIOS) {
      // منع التقاط الشاشة على iOS
      await screen_protector.ScreenProtector.preventScreenshotOn();

      // حماية من تسرب البيانات عند وضع التطبيق في الخلفية
      await screen_protector.ScreenProtector.protectDataLeakageWithBlur();
    }
  } catch (e) {
    debugPrint('Screen protector initialization error: $e');
    // تجاهل الأخطاء هنا لأن ScreenshotPrevention تعمل كطبقة أساسية
  }

  // منع تسجيل الشاشة على مستوى النظام (iOS فقط)
  if (Platform.isIOS) {
    try {
      const MethodChannel('com.app.noorbiology/security')
          .invokeMethod('enableScreenProtection');
    } catch (e) {
      debugPrint('Error enabling screen protection: $e');
    }
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ConnectivityProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final ScreenshotPrevention _screenshotPrevention = ScreenshotPrevention();
  bool _isScreenshotDetected = false;
  Timer? _resetTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // إضافة مستمع لاكتشاف التقاط الشاشة
    _screenshotPrevention
        .addScreenshotDetectedListener(_handleScreenshotDetected);
  }

  // معالجة اكتشاف التقاط الشاشة
  void _handleScreenshotDetected() {
    setState(() {
      _isScreenshotDetected = true;
    });

    // إعادة تعيين الحالة بعد 3 ثوان
    _resetTimer?.cancel();
    _resetTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isScreenshotDetected = false;
        });
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // إعادة تفعيل الحماية عند تغيير حالة التطبيق
    switch (state) {
      case AppLifecycleState.resumed:
        // إعادة تفعيل الحماية عند استئناف التطبيق
        _screenshotPrevention.enableProtection();
        break;
      case AppLifecycleState.paused:
        // تعزيز الحماية عند وضع التطبيق في الخلفية
        if (Platform.isIOS) {
          try {
            screen_protector.ScreenProtector.protectDataLeakageWithBlur();
          } catch (e) {
            debugPrint('Screen protector blur error: $e');
          }
        }
        break;
      case AppLifecycleState.inactive:
        // تعزيز الحماية عند تعطيل التطبيق مؤقتًا
        if (Platform.isIOS) {
          try {
            screen_protector.ScreenProtector.protectDataLeakageWithBlur();
          } catch (e) {
            debugPrint('Screen protector blur error: $e');
          }
        }
        break;
      case AppLifecycleState.detached:
        // لا شيء خاص هنا
        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _screenshotPrevention
        .removeScreenshotDetectedListener(_handleScreenshotDetected);
    _resetTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedSecureScreen(
      enforceProtection: true,
      child: MaterialApp(
        title: 'نور البيولوجي',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.green,
          fontFamily: 'Cairo',
          useMaterial3: true,
        ),
        locale: const Locale('ar', 'EG'),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ar', 'EG'),
          Locale('en', 'US'),
        ],
        builder: (context, child) {
          // إضافة طبقة تحذير عند اكتشاف التقاط الشاشة أو تسجيلها
          return Stack(
            textDirection: TextDirection.rtl,
            children: [
              child!,
              if (_isScreenshotDetected)
                Positioned.fill(
                  child: Material(
                    color: Colors.black.withAlpha(230),
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.security,
                            color: Colors.red,
                            size: 60,
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            'تم اكتشاف محاولة التقاط الشاشة!',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 10),
                          const Text(
                            'التقاط الشاشة غير مسموح لأسباب أمنية',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _isScreenshotDetected = false;
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('إغلاق'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              // إضافة طبقة تحذير عند اكتشاف تسجيل الشاشة
              if (_screenshotPrevention.isRecording)
                Positioned.fill(
                  child: Material(
                    color: Colors.black.withAlpha(230),
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.videocam_off,
                            color: Colors.red,
                            size: 60,
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            'تم اكتشاف تسجيل الشاشة!',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 10),
                          const Text(
                            'تسجيل الشاشة غير مسموح لأسباب أمنية',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 10),
                          const Text(
                            'يرجى إيقاف تسجيل الشاشة للاستمرار',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
        home: const SplashScreen(),
      ),
    );
  }
}
