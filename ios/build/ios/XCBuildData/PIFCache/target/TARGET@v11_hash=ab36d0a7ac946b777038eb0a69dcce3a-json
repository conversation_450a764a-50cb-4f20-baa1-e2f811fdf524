{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844a3a18aab79ffd26782cf285458cf9c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98edbb64d4500b358231b8fa04affb1f73", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98edbb64d4500b358231b8fa04affb1f73", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c62179e2549d36faaf317d1e23748f86", "guid": "bfdfe7dc352907fc980b868725387e9840a9caeba178add7aa4dceb4c1bb75fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac3c48c6be2c339bd43ed33d758639c6", "guid": "bfdfe7dc352907fc980b868725387e98b4c61cc35fba40b4ce329132c3f61eca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890bf1bfd618c0c932672034198c9d3e8", "guid": "bfdfe7dc352907fc980b868725387e9818da142fe82d8a78c7be9db931d371ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce14616b5bf353c30e0b48916559339c", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7b86769c3bff1567e9b6c3541029e60", "guid": "bfdfe7dc352907fc980b868725387e984b4ae0c89031a24d1d71288e24a797ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df2d963a3443b635c5f39c23ecc3d457", "guid": "bfdfe7dc352907fc980b868725387e985f28519c628b2857ebb37b7b581178d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340ff8511df3642be6e1c738087bf83d", "guid": "bfdfe7dc352907fc980b868725387e98d488e0a22f4c14aacdf4d40c2c9ddb44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdcbf0b994c7047d761c7d0c70ecd071", "guid": "bfdfe7dc352907fc980b868725387e98fe7273ad9aa9fdcb548c9c43dc93f8b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883ff76dbd86268a380e1d2459653348", "guid": "bfdfe7dc352907fc980b868725387e98313d9e25b0bf5f3c97ad080d07ef3914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ee662b07996872966fc0167573b8fe", "guid": "bfdfe7dc352907fc980b868725387e989f407ff8592b35f500a6527a20317817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd9f93200c857435f6c432b0638d2f6", "guid": "bfdfe7dc352907fc980b868725387e98e16efa990e491cc807ce6fc10625d15b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc7a872948acf55c5187a5b3c3552b87", "guid": "bfdfe7dc352907fc980b868725387e98105db008f99641fadb0caf5f7506b3c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4a33766fb9ea2f5ed62dfd40ef1b22e", "guid": "bfdfe7dc352907fc980b868725387e98a6e4e157926dc87d2e4f2973b18d8bcc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b151ddda7f0bd17a56dc48bce55c4705", "guid": "bfdfe7dc352907fc980b868725387e98d1ddc3be1007fb9ee70d27ff32e03352", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98680169960f4a808d4deae94aee837e66", "guid": "bfdfe7dc352907fc980b868725387e980f430574f80b3377bafb28f408603313", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c006e373e7d1344a8175901aea1ce4a2", "guid": "bfdfe7dc352907fc980b868725387e9811c36f63ef52dcfc9629b13ac9cd2366", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff8f3de49d446d5cf6af862c178db9b", "guid": "bfdfe7dc352907fc980b868725387e98fdcde1de031b3934e8ad535d6a77715a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5a4dbaf2ac68d0de2fa612f7bbccf73", "guid": "bfdfe7dc352907fc980b868725387e9806312812ff309343658d470012c9949e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f55269d162295fc6d65d456b05dfce2", "guid": "bfdfe7dc352907fc980b868725387e98fd547b1fbc8601ed045148719c9fcb31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986707ee291e898d3f97ccc85c15a4cfd8", "guid": "bfdfe7dc352907fc980b868725387e98b8107d30d41b046eabedd15b3e204207", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b0e048209d53cd94ffdc7b376386129", "guid": "bfdfe7dc352907fc980b868725387e985911bdce5650368ba78e000854a76edc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55ba806f9455ada28c48d2b67ce5378", "guid": "bfdfe7dc352907fc980b868725387e9851879a7bd58d5db494aaffdae14944fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dfb1c3d7a62d40808ed3fb329e2b1ec", "guid": "bfdfe7dc352907fc980b868725387e986d5e368a2966e687c64f43486e1a14ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2bc8c726592915cbd6ae687f92560d9", "guid": "bfdfe7dc352907fc980b868725387e9892ad5fea3cd9806e3d88c69c494e5724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a597d5bb01a97a1e24d42ad0ab9f9f99", "guid": "bfdfe7dc352907fc980b868725387e982287d5443baa60a644e03eff995b0dbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876256218c0be3f5b4cbc35f58248f1ca", "guid": "bfdfe7dc352907fc980b868725387e984b7cfa5cae6af5dd957a2e5e2c72d887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b5165fb1d6f3c07df14f797028d14b2", "guid": "bfdfe7dc352907fc980b868725387e981354bfb9c1f8a7cd29cda84a949c2b50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f42fed0246bf38f847f078debf45a6a7", "guid": "bfdfe7dc352907fc980b868725387e988ea9c6211d68a85a4fa5661a00b70d12", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea94631b3f724e25bac50a66286bb07b", "guid": "bfdfe7dc352907fc980b868725387e988601a4ee62b9ae3a9bcf8cd3d0ebe162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0a5bb4a7cc8232c1856caba11b4c8f3", "guid": "bfdfe7dc352907fc980b868725387e98118fac16cfc7d0d72dbd7690930ee944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aadb5e6e54e80c1a95ad5f9db2461e08", "guid": "bfdfe7dc352907fc980b868725387e988018d42a6cbf30bd26714051d751756f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c372ac0b8388faa738e163149dae94", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98376c0489f11c597e9b74c13cc71abc29", "guid": "bfdfe7dc352907fc980b868725387e98f8b7fbab77fdbedd35cf16a5f9b711bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7cb8e75ff27b266e244d9901d9d453", "guid": "bfdfe7dc352907fc980b868725387e9865826dabae77a65c0f8e3f8837afaccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a3bbb211bd749e5420e891879cb23ae", "guid": "bfdfe7dc352907fc980b868725387e98b2432585b08dbc72dc97577aadf81be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814f98a008df7f949065ce4eb39a9e452", "guid": "bfdfe7dc352907fc980b868725387e98dea84ba5702c5f8810e62a4263d9ace3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50aa00a8f3ce20adbebc85bd94838ce", "guid": "bfdfe7dc352907fc980b868725387e9838e63e5a34be3fcb50907fe5f2f4afed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821922ef598dedc7efb9a1dc9000cfe7e", "guid": "bfdfe7dc352907fc980b868725387e983efddb9b6fee895c6ea68388a011affc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b20bb6edec213a47625ce4b9eeba45", "guid": "bfdfe7dc352907fc980b868725387e989a76fbeb6bac5b3b1280afb834ae8aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c3b6a914075d12b2144f60f154428a3", "guid": "bfdfe7dc352907fc980b868725387e98ec6c6d0bf1756896b53daee803877721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8df69b026c3b3f72d59c5f863bbfdf6", "guid": "bfdfe7dc352907fc980b868725387e98e74fcc69d8150f502ccf1b3e2fc5485f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892db867ab1fe6325694bd5905d8e75ec", "guid": "bfdfe7dc352907fc980b868725387e98b821742c1f6f7ea4cd9c0b273279b76f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819c160248512dc1085504d8291e6965c", "guid": "bfdfe7dc352907fc980b868725387e982dc177db9f7d9ffb96479a0947cb42c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7a6b18f4f0a1e84de115c5841b7e0e", "guid": "bfdfe7dc352907fc980b868725387e9804e1e82783b73939a38d0ed8e1238d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ceb5dfecb5a07c9f21fc26d982b978a", "guid": "bfdfe7dc352907fc980b868725387e9809531441d9135d8d86d4ea73bb600633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802eaac904979f1cdc900d52c558bdbe0", "guid": "bfdfe7dc352907fc980b868725387e986708b4f0dbacc243fb99a7eb06925479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826dfeb8a9d4b0d189e9517d16e07a0fa", "guid": "bfdfe7dc352907fc980b868725387e98670d25b97004c7a93f78975edd086e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e67af1beaa6d78090935d693f6364749", "guid": "bfdfe7dc352907fc980b868725387e98f1a030d6e6eb0600473a3e08d860ea34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c98d4a67c24825fbd2f18a84e2aafca", "guid": "bfdfe7dc352907fc980b868725387e98cc7934ae79503605f0492de7b872114f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c9b9b232921beec12d58ea39e01eee", "guid": "bfdfe7dc352907fc980b868725387e98fbf436204971d95f60e7a593d7540c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888d845531ea60a57a5928bb968512c24", "guid": "bfdfe7dc352907fc980b868725387e9898aaa51c1a99ce45e80eb8f93420d71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984940a02b1972169d7ef3ef1835fb143f", "guid": "bfdfe7dc352907fc980b868725387e98d0c2773f09c4cabb2cf0eebceee42fe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6a555ea84053413989df73ec0265a9", "guid": "bfdfe7dc352907fc980b868725387e98da7585a197ccba31a23ef6e300e35684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e8215e475986ed20d79a914c9fb8c4a", "guid": "bfdfe7dc352907fc980b868725387e9877adb990468abb172a1f126cb52f85d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96cfb8465f55ac265cb3c51854df7c6", "guid": "bfdfe7dc352907fc980b868725387e98cf068f37d84ba0e155873865791a4c16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce5f73d480f56fc577609b653e6d7720", "guid": "bfdfe7dc352907fc980b868725387e982b47bac972003ddff7a9354e299d2485"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986281e55104e3c847c31b37f6edd9851f", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}