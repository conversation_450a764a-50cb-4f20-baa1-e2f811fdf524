{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c1a9b937cfb0eeec2c49a12356c742b3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b0491debad13020a165ab8d8fb8e7a6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98908ef70f19e69f49fc6858aaf79a9fcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5d17fb1d91f0a4f64158124b05fa286", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98908ef70f19e69f49fc6858aaf79a9fcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d48975f249f96592ebac62cabeb71693", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989be92edc006d8b8b666b1365d0bdad03", "guid": "bfdfe7dc352907fc980b868725387e9883c10ad6045e2edb3b45c1c6c0413b92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a4fdc38bf986b19d561fd705b949d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a76d74ec42014d927664a966bcd89ecb", "guid": "bfdfe7dc352907fc980b868725387e9862651134a512e8cacd066e88486ee781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f3987a3f26c202de9e54d1d08a1a702", "guid": "bfdfe7dc352907fc980b868725387e98e46a3ea7c8d8e3c4f58bd225b3909de3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98735de6f1aa898aeac8072739276309c1", "guid": "bfdfe7dc352907fc980b868725387e984c4817cc7a5d092968c19fcdb2b6459e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980328e33a97e6d1935bc86370c6c77235", "guid": "bfdfe7dc352907fc980b868725387e983c2e16d3e610a0d0c22405eccb293da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333dba664d864d24381386bfc1050632", "guid": "bfdfe7dc352907fc980b868725387e98782cf73af5809191cf4eba4b8215b32e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e36578a7512baab92fd747db19b78e7e", "guid": "bfdfe7dc352907fc980b868725387e98b07a1f3502a251b0026ba5261d9892c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d48512faff2a16004dfdbc158697be2", "guid": "bfdfe7dc352907fc980b868725387e98497a11d603ca182693f00eddf1f6ce9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d33d857730ed633e4da8c3e18a90de59", "guid": "bfdfe7dc352907fc980b868725387e9880b1b38c7f5676aa119fde5cc259eab3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b712935648a83370e434b786c520c26", "guid": "bfdfe7dc352907fc980b868725387e981dd43cb2063d9cd3afa56d7f66c4ba25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc7f3126e0f92500f8fb97d85ae61c7", "guid": "bfdfe7dc352907fc980b868725387e98a8fe11436d7a2f3eebe3f405b66dbdff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30207b6f1584f7168e44e68b3d39ff0", "guid": "bfdfe7dc352907fc980b868725387e98e73df0f8362d1d943d6e1181b802b3a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98645613d324395e9f8aab16d51bfd896f", "guid": "bfdfe7dc352907fc980b868725387e9856b7b969cf7c20623183c368463a697b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849837a3e00b6d216cf9f2e143d094e67", "guid": "bfdfe7dc352907fc980b868725387e98643821d4052111904f32bf5c442380e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b917bd37d7e951e017548ff2855677", "guid": "bfdfe7dc352907fc980b868725387e989868af65fb25ae54ae8e0f38bfacba0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803941b770bdcd6b0a51a453554369a52", "guid": "bfdfe7dc352907fc980b868725387e98802d438d9ddbd77c9601785dbb7d394c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deebbe514227518fbc9925309fc81e9f", "guid": "bfdfe7dc352907fc980b868725387e9882aab3dea4052c1c5bf2cc71658ea085"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c5598b85b418355d20052f726ac535", "guid": "bfdfe7dc352907fc980b868725387e98f742f3ae402ddf25d7f25ac225f31e7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848cfad978034479c3db1d9ad24fdea8e", "guid": "bfdfe7dc352907fc980b868725387e98e375d2c740dc56fa3928f7b4e5fd38b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca53b8dcb3d1022c44609bf1197ec5a0", "guid": "bfdfe7dc352907fc980b868725387e9875f982d1f53eafa32e61506e179df012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c4d5008e77e0d91093c0ed92050b7c0", "guid": "bfdfe7dc352907fc980b868725387e985712f61a919fd0546509996887119b3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983010fcaff2a9db5f89a7554b7ce24b45", "guid": "bfdfe7dc352907fc980b868725387e9838f1e89de5b780beff7f2ab0da85ab51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef5db0a451bd548deb5774adfd403ad3", "guid": "bfdfe7dc352907fc980b868725387e988154a78674a081fd2b1bae575d97ea21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980260b812da05031f1c39ebee0e98b71f", "guid": "bfdfe7dc352907fc980b868725387e98c20c76275a46289820cec64517207036"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b693edc2e4dcf80e2606f34558fa5f5", "guid": "bfdfe7dc352907fc980b868725387e9888238703fd52b8f0555517d53f3bb6ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28f3eac314d6732e4bd7f99d73fc1f9", "guid": "bfdfe7dc352907fc980b868725387e989e413a6baba9c1bf849f2af5499ce929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858d0138f9dae2d0b8628bb80f86a855a", "guid": "bfdfe7dc352907fc980b868725387e98504bb4671717bcd3008dac00589d1ee5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870261a880f15a50ea06f1bbfa8eb0285", "guid": "bfdfe7dc352907fc980b868725387e9862ce2a6660e35ae0787ea311f2990ae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884bd99967a78829d0c31c8fd5c464b46", "guid": "bfdfe7dc352907fc980b868725387e983c0f40906c053a846f1dd431125e1d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a043f7cf776c46eaa3488495f2f04a2f", "guid": "bfdfe7dc352907fc980b868725387e985df657a9042e8c9e4cda282b86c5ff73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ccddca2ba75b41f8168fcadd19778f3", "guid": "bfdfe7dc352907fc980b868725387e98bc3028553d295cec3f77a3aa86f31da6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd71f99e80d3ae5c27cd485aee2f36d9", "guid": "bfdfe7dc352907fc980b868725387e980532408a5977baceffd485c445e2786b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98605e968ec265e113ae6851f58c9da81c", "guid": "bfdfe7dc352907fc980b868725387e9885b271a99769685c618f5c8b4755eb52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367dc5eed088e36ff2a5770b1b1eedd3", "guid": "bfdfe7dc352907fc980b868725387e98bfc24a8b5ffa1148bf938220aeb2446a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833a0b8ec6d37f16bccad41eea22a5b29", "guid": "bfdfe7dc352907fc980b868725387e981c4bd8d279e2891906d36301438a9802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989264df1c33dcd09e756ed7d8a02d00cb", "guid": "bfdfe7dc352907fc980b868725387e9832c964d6b6d3fe2b1dfcc12fa6a6c68f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebfba5f1b7597a34e23da7584e36f9ae", "guid": "bfdfe7dc352907fc980b868725387e98fb44c96e0c18e64269da26c34c9c3854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855ce89ac3b6c285375d633a3cbf7ebde", "guid": "bfdfe7dc352907fc980b868725387e986000b6b5d65d9674e29a05e714e77645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c39718fef36026b00938af18324a926e", "guid": "bfdfe7dc352907fc980b868725387e983b6d66cd85eaff4115b37894cd1909d7"}], "guid": "bfdfe7dc352907fc980b868725387e98579fbecb47bc990731107675682637c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986281e55104e3c847c31b37f6edd9851f", "guid": "bfdfe7dc352907fc980b868725387e9817874d657b3f6cd3a4f0acf89676b8a6"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e0578284590adbf8b73f59c73cb7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acb6d21f8b6048502a790d59f4fc84cb", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98be611f6cbf3440ae2e558dafe4f7c459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}