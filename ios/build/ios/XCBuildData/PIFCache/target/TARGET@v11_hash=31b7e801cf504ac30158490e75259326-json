{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9835b071fdfcee0973c9098519419e7577", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee49f65d26d8a0c930cc03d16e0ffcc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e80d914ba90fd37937f288f4ed1e7015", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d3fcd01a34fff9e18dec0764bcc371e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e80d914ba90fd37937f288f4ed1e7015", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984def481d54810d0de6d3335b228b2a5d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c294d2dafba248e5744e4e92feee92ca", "guid": "bfdfe7dc352907fc980b868725387e98f74bfe561cdc142d140be02f934f1dd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988727ccfd40b5f040f669f19522ab0e14", "guid": "bfdfe7dc352907fc980b868725387e98ddd1acc6cab6fa1a3ae6ff68c2ee7ffd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4954d6d2d3cae4be4f0a9474916502f", "guid": "bfdfe7dc352907fc980b868725387e9888cc39c2354593780bbb95bb3d90af77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802c96939ff5c569d4e6bc4212646419f", "guid": "bfdfe7dc352907fc980b868725387e98f9c1b7199519e7af996f9ba6b03d6be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9d3a5cdfafbcf4db7a3174dbf630e9e", "guid": "bfdfe7dc352907fc980b868725387e98dbc699cb2155c89c809d3a8dd67a3040"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd38453c39af1139c6860c7909c8bc85", "guid": "bfdfe7dc352907fc980b868725387e98b1c8158d3c8f714191f1bc30a2ec310c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984680c185b6ff6789d19290ad49e302f0", "guid": "bfdfe7dc352907fc980b868725387e98ed388d1060e055ee0229597e31fb56cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982257e683dcc5d34d4ff0a65759e2cb5b", "guid": "bfdfe7dc352907fc980b868725387e98da9f0989793cf8c015e923328f39395d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f21547aaf8d437576bf7f1e4540d8af", "guid": "bfdfe7dc352907fc980b868725387e9829416604eb00063552d60237f822e8b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2634b5c23556f5e710c4ce1a38ee3e", "guid": "bfdfe7dc352907fc980b868725387e985cba8579a97467f9754243ca7386c6b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffdb164006b29d56a64c382f10feecdb", "guid": "bfdfe7dc352907fc980b868725387e9877d7c384acb9a41b0d0cc9a2b6a60671", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823afed30ac25fb8715837fb294b0dd44", "guid": "bfdfe7dc352907fc980b868725387e987772929e02f2bc40031dea18f328e61a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809568f8c39009b85694047b75b3117d0", "guid": "bfdfe7dc352907fc980b868725387e9883096457fe8f9b173c6178c778d6339b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98048f894995f2d206439b861022a3eb17", "guid": "bfdfe7dc352907fc980b868725387e983f3be03f3e7b7acb38181c24f52ae021", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cc465b98567e5be1dff8b7284a07e4e3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9827d254a130b0b6748ab5bf1ab9ac0b30", "guid": "bfdfe7dc352907fc980b868725387e985adaa6c4e40f33315ac0cec984200988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c916c65d9ec2c6a3d7087581b8bc914b", "guid": "bfdfe7dc352907fc980b868725387e980c12ce86271f63e5d7dfac59f166b554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ba052fdef6356f3001be6f36ac6ae3c", "guid": "bfdfe7dc352907fc980b868725387e983c4f06e771375bb6003300e3c605b4c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c457717f8565125c1bde93f1e32fa8", "guid": "bfdfe7dc352907fc980b868725387e98bee51503428d700e8481b3c7cef3c677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bce317475dc6bab5ea973900543c0edb", "guid": "bfdfe7dc352907fc980b868725387e98a1d0b829ca863c9c9aece2f4c39d850e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984232987de7c3bcff132ba1ae30493818", "guid": "bfdfe7dc352907fc980b868725387e98c6139b2e3f02c9e801714b7f534d6378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a1e15b020381214c8b06dfdafc8fc7b", "guid": "bfdfe7dc352907fc980b868725387e98996b601e71b4aa302cd1e333254fb4bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bd9c2f0c363cc0540b91cae0a136664", "guid": "bfdfe7dc352907fc980b868725387e98f0a1bf52b95ad1a471c93e8f94b93cc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982069286148a7613175249d0264d57ea3", "guid": "bfdfe7dc352907fc980b868725387e9890cce246e4aa448765e1bfd2b54e15b7"}], "guid": "bfdfe7dc352907fc980b868725387e9837c2f0a37c50e959478519168227e455", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986281e55104e3c847c31b37f6edd9851f", "guid": "bfdfe7dc352907fc980b868725387e982f461d51c284a55b8f869fc9092ae5dc"}], "guid": "bfdfe7dc352907fc980b868725387e98dd47f73652ff7b522b7942f6a87afd23", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d17544c34b81de618417de5f9c91b4ec", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e98e60a652c76bfee084293e97b00176921", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}