import UIKit
import Flutter
import AVFoundation
import WebKit

@main
class AppDelegate: FlutterAppDelegate {
    private var securityChannel: FlutterMethodChannel?

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // تسجيل الإضافات المولدة تلقائيًا
        GeneratedPluginRegistrant.register(with: self)

        // إعداد قناة الاتصال مع Flutter
        setupMethodChannel()

        // تفعيل منع تسجيل الشاشة على مستوى التطبيق
        setupScreenRecordingPrevention()

        // إضافة مراقب لالتقاط الشاشة
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleScreenCaptureNotification),
            name: UIScreen.capturedDidChangeNotification,
            object: nil
        )

        // استخدام النافذة الآمنة
        setupSecureWindow()

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    // إعداد قناة الاتصال مع Flutter
    private func setupMethodChannel() {
        let controller = window?.rootViewController as? FlutterViewController
        if let controller = controller {
            securityChannel = FlutterMethodChannel(
                name: "com.app.noorbiology/security",
                binaryMessenger: controller.binaryMessenger
            )

            securityChannel?.setMethodCallHandler { [weak self] (call, result) in
                guard let self = self else { return }

                if call.method == "enableScreenProtection" {
                    // تفعيل حماية الشاشة
                    self.enableScreenProtection()
                    result(true)
                } else {
                    result(FlutterMethodNotImplemented)
                }
            }
        }
    }

    // تفعيل حماية الشاشة
    private func enableScreenProtection() {
        // تطبيق إجراءات إضافية لمنع التقاط الشاشة
        if #available(iOS 13.0, *) {
            if UIScreen.main.isCaptured {
                // إرسال إشعار إلى Flutter
                securityChannel?.invokeMethod("screenCaptureDetected", arguments: nil)
            }
        }
    }

    // إعداد النافذة الآمنة
    private func setupSecureWindow() {
        // بدلاً من استخدام نافذة مخصصة، نقوم بتعزيز النافذة الحالية
        if let window = self.window {
            // إضافة مراقب لحالة تسجيل الشاشة للنافذة الحالية
            if #available(iOS 13.0, *) {
                NotificationCenter.default.addObserver(
                    self,
                    selector: #selector(handleScreenCaptureChange),
                    name: UIScreen.capturedDidChangeNotification,
                    object: nil
                )
            }

            // إضافة مراقب للتقاط الشاشة
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleScreenshotNotification),
                name: UIApplication.userDidTakeScreenshotNotification,
                object: nil
            )

            // تطبيق إعدادات أمان إضافية على النافذة الحالية
            window.layer.allowsGroupOpacity = false

            // منع التقاط الشاشة باستخدام تقنيات متقدمة
            applyAdvancedScreenshotPrevention(to: window)

            // إضافة طبقة حماية شفافة
            let securityLayer = CALayer()
            securityLayer.frame = window.bounds
            securityLayer.backgroundColor = UIColor.clear.cgColor
            securityLayer.name = "SecurityLayer"
            window.layer.addSublayer(securityLayer)

            // تطبيق حماية إضافية على مستوى النافذة
            makeWindowSecure(window)
        }
    }

    // تطبيق حماية متقدمة لمنع التقاط الشاشة
    private func applyAdvancedScreenshotPrevention(to window: UIWindow) {
        // إنشاء طبقة حماية متقدمة
        let secureView = UIView(frame: window.bounds)
        secureView.tag = 777
        secureView.isUserInteractionEnabled = false
        secureView.backgroundColor = UIColor.clear

        // إضافة طبقة تشويش بصري خفيفة جدًا
        let noiseLayer = CALayer()
        noiseLayer.frame = secureView.bounds

        // إنشاء نمط تشويش بصري
        let filter = CIFilter(name: "CIRandomGenerator")
        let noiseImage = filter?.outputImage
        let scale = UIScreen.main.scale
        let context = CIContext(options: nil)

        if let noiseImage = noiseImage, let cgImage = context.createCGImage(noiseImage, from: CGRect(x: 0, y: 0, width: 100, height: 100)) {
            let patternImage = UIImage(cgImage: cgImage)
            noiseLayer.contents = patternImage.cgImage
            noiseLayer.opacity = 0.015 // شفافية عالية جدًا
            secureView.layer.addSublayer(noiseLayer)
        }

        // إضافة طبقة الحماية إلى النافذة
        window.addSubview(secureView)
        window.bringSubviewToFront(secureView)
    }

    // تطبيق إعدادات الأمان على النافذة
    private func makeWindowSecure(_ window: UIWindow) {
        // تعطيل خاصية allowsGroupOpacity لمنع التقاط الشاشة
        window.layer.allowsGroupOpacity = false

        // تعطيل خاصية shouldRasterize لمنع التقاط الشاشة
        window.layer.shouldRasterize = false

        // تعطيل خاصية drawsAsynchronously لمنع التقاط الشاشة
        window.layer.drawsAsynchronously = false

        // تعطيل خاصية allowsEdgeAntialiasing لمنع التقاط الشاشة
        window.layer.allowsEdgeAntialiasing = false
    }

    // معالجة تغيير حالة التقاط الشاشة
    @objc private func handleScreenCaptureChange() {
        if #available(iOS 13.0, *) {
            if UIScreen.main.isCaptured {
                // تنفيذ إجراءات عند اكتشاف تسجيل الشاشة
                applySecurityMeasures()
            }
        }
    }

    // دالة لإعداد منع تسجيل الشاشة
    private func setupScreenRecordingPrevention() {
        let mainQueue = DispatchQueue.main

        // مراقبة حالة تسجيل الشاشة
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleScreenRecordingStatusChanged),
            name: UIScreen.capturedDidChangeNotification,
            object: nil
        )

        // مراقبة التقاط الشاشة (للإصدارات القديمة من iOS)
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleScreenshotNotification),
            name: UIApplication.userDidTakeScreenshotNotification,
            object: nil
        )

        // إعداد مراقبة دورية لحالة تسجيل الشاشة (بفاصل زمني أقصر للكشف السريع)
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            mainQueue.async {
                self?.checkScreenRecordingStatus()
            }
        }

        // تطبيق حماية إضافية عند بدء التشغيل
        if #available(iOS 13.0, *) {
            if UIScreen.main.isCaptured {
                // تنفيذ إجراءات فورية إذا كان التسجيل نشطًا بالفعل
                applySecurityMeasures()
            }
        }
    }

    // معالجة إشعار التقاط الشاشة (للإصدارات القديمة من iOS)
    @objc private func handleScreenshotNotification() {
        // تنفيذ إجراءات عند التقاط الشاشة
        applySecurityMeasures()
    }

    // التحقق من حالة تسجيل الشاشة
    private func checkScreenRecordingStatus() {
        let isRecording = UIScreen.main.isCaptured
        if isRecording {
            // تنفيذ إجراءات إضافية عند اكتشاف تسجيل الشاشة
            applySecurityMeasures()
        }
    }

    // معالجة تغيير حالة تسجيل الشاشة
    @objc private func handleScreenRecordingStatusChanged() {
        if UIScreen.main.isCaptured {
            // تنفيذ إجراءات عند بدء تسجيل الشاشة
            applySecurityMeasures()
        }
    }

    // معالجة إشعار التقاط الشاشة
    @objc private func handleScreenCaptureNotification() {
        if UIScreen.main.isCaptured {
            // تنفيذ إجراءات عند التقاط الشاشة
            applySecurityMeasures()
        }
    }

    // تطبيق إجراءات أمنية إضافية
    private func applySecurityMeasures() {
        // يمكن إضافة إجراءات إضافية هنا مثل:
        // - تعتيم المحتوى الحساس
        // - عرض رسالة تحذير
        // - تسجيل محاولة الالتقاط

        // إضافة طبقة تعتيم للشاشة
        addDimOverlay()

        // إرسال إشعار إلى Flutter
        securityChannel?.invokeMethod("screenCaptureDetected", arguments: nil)
    }

    // إضافة طبقة تعتيم للشاشة
    private func addDimOverlay() {
        // إزالة الطبقة السابقة إن وجدت
        removeDimOverlay()

        // إنشاء طبقة تعتيم
        let overlay = UIView(frame: UIScreen.main.bounds)
        overlay.backgroundColor = UIColor.black.withAlphaComponent(0.98) // تعتيم أكثر
        overlay.tag = 999

        // إضافة رسالة تحذير
        let label = UILabel(frame: CGRect(x: 20, y: UIScreen.main.bounds.height/2 - 50, width: UIScreen.main.bounds.width - 40, height: 100))
        label.text = "تسجيل الشاشة غير مسموح لأسباب أمنية\nScreen recording is not allowed for security reasons"
        label.textAlignment = .center
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        label.numberOfLines = 0

        // إضافة أيقونة تحذير
        let imageView = UIImageView(frame: CGRect(x: (UIScreen.main.bounds.width - 60) / 2, y: label.frame.origin.y - 80, width: 60, height: 60))
        let warningConfig = UIImage.SymbolConfiguration(pointSize: 60, weight: .bold)
        imageView.image = UIImage(systemName: "exclamationmark.shield.fill", withConfiguration: warningConfig)?.withTintColor(.red, renderingMode: .alwaysOriginal)

        overlay.addSubview(imageView)
        overlay.addSubview(label)

        // إضافة تأثير اهتزاز للتحذير
        UIView.animate(withDuration: 0.1, delay: 0, options: [.repeat, .autoreverse], animations: {
            imageView.transform = CGAffineTransform(translationX: 5, y: 0)
        }, completion: nil)

        // إضافة زر إغلاق
        let closeButton = UIButton(type: .system)
        closeButton.frame = CGRect(x: (UIScreen.main.bounds.width - 150) / 2, y: label.frame.maxY + 20, width: 150, height: 40)
        closeButton.setTitle("إغلاق التطبيق", for: .normal)
        closeButton.setTitleColor(.white, for: .normal)
        closeButton.backgroundColor = UIColor.red
        closeButton.layer.cornerRadius = 8
        closeButton.addTarget(self, action: #selector(forceCloseApp), for: .touchUpInside)
        overlay.addSubview(closeButton)

        // إضافة الطبقة إلى النافذة الرئيسية
        if let window = UIApplication.shared.windows.first {
            window.addSubview(overlay)

            // تطبيق تأثير تشويش إضافي
            applyDistortionEffect(to: overlay)

            // لا نزيل الطبقة تلقائيًا في حالة تسجيل الشاشة
            if !UIScreen.main.isCaptured {
                // إزالة الطبقة بعد 4 ثوان فقط إذا لم يكن هناك تسجيل للشاشة
                DispatchQueue.main.asyncAfter(deadline: .now() + 4.0) {
                    // تلاشي تدريجي للطبقة
                    UIView.animate(withDuration: 0.5, animations: {
                        overlay.alpha = 0
                    }, completion: { _ in
                        self.removeDimOverlay()
                    })
                }
            }
        }
    }

    // تطبيق تأثير تشويش إضافي
    private func applyDistortionEffect(to view: UIView) {
        // إنشاء طبقة تشويش متحركة
        let distortionLayer = CALayer()
        distortionLayer.frame = view.bounds
        distortionLayer.name = "DistortionLayer"

        // إنشاء نمط تشويش متحرك
        let filter = CIFilter(name: "CIRandomGenerator")
        let noiseImage = filter?.outputImage
        let context = CIContext(options: nil)

        if let noiseImage = noiseImage, let cgImage = context.createCGImage(noiseImage, from: CGRect(x: 0, y: 0, width: 200, height: 200)) {
            let patternImage = UIImage(cgImage: cgImage)
            distortionLayer.contents = patternImage.cgImage
            distortionLayer.opacity = 0.1

            // إضافة حركة للتشويش
            let animation = CABasicAnimation(keyPath: "position")
            animation.duration = 0.1
            animation.repeatCount = Float.infinity
            animation.autoreverses = true
            animation.fromValue = NSValue(cgPoint: CGPoint(x: 0, y: 0))
            animation.toValue = NSValue(cgPoint: CGPoint(x: 5, y: 5))
            distortionLayer.add(animation, forKey: "position")

            view.layer.addSublayer(distortionLayer)
        }
    }

    // إغلاق التطبيق قسريًا
    @objc private func forceCloseApp() {
        exit(0)
    }

    // إزالة طبقة التعتيم
    private func removeDimOverlay() {
        if let window = UIApplication.shared.windows.first, let overlay = window.viewWithTag(999) {
            overlay.removeFromSuperview()
        }
    }

    override func applicationWillResignActive(_ application: UIApplication) {
        // تطبيق إجراءات أمنية عند خروج التطبيق من الحالة النشطة
        super.applicationWillResignActive(application)

        // تفعيل حماية إضافية عند الخروج من التطبيق
        if #available(iOS 13.0, *) {
            // إضافة طبقة تعتيم مؤقتة لمنع التقاط الشاشة أثناء تبديل التطبيقات
            let blurOverlay = UIVisualEffectView(effect: UIBlurEffect(style: .dark))
            blurOverlay.frame = UIScreen.main.bounds
            blurOverlay.tag = 888

            if let window = UIApplication.shared.windows.first {
                window.addSubview(blurOverlay)
            }
        }
    }

    override func applicationDidBecomeActive(_ application: UIApplication) {
        // إعادة تفعيل الإجراءات الأمنية عند عودة التطبيق للحالة النشطة
        super.applicationDidBecomeActive(application)

        // إزالة طبقة التعتيم المؤقتة
        if let window = UIApplication.shared.windows.first, let blurOverlay = window.viewWithTag(888) {
            blurOverlay.removeFromSuperview()
        }

        // إعادة تفعيل الحماية
        enableScreenProtection()

        // التحقق من حالة تسجيل الشاشة
        checkScreenRecordingStatus()
    }
}
