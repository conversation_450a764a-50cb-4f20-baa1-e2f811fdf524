<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>منصة الاستاذه نور محمد</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>منصة الاستاذه نور محمد</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tg</string>
		<string>https</string>
		<string>http</string>
		<string>tel</string>
		<string>mailto</string>
		<string>whatsapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>تم منع تصوير الشاشة لأسباب أمنية</string>
	<key>NSScreenCaptureUsageDescription</key>
	<string>تم منع تسجيل الشاشة لأسباب أمنية</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>تم منع حفظ الصور لأسباب أمنية</string>
	<key>NSCameraUsageDescription</key>
	<string>تم منع استخدام الكاميرا لأسباب أمنية</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>تم منع استخدام الميكروفون لأسباب أمنية</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSAllowsArbitraryLoads</key>
	<false/>
	<key>UIRequiresPersistentWiFi</key>
	<true/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<false/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<false/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
	</dict>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIScreenCapturePreventionSupported</key>
	<true/>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIViewEdgeAntialiasing</key>
	<false/>
	<key>UIViewGroupOpacity</key>
	<false/>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
	</array>
	<key>UIApplicationSupportsSecureBlending</key>
	<true/>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict/>
	</dict>
</dict>
</plist>
