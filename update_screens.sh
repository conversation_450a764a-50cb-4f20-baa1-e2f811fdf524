#!/bin/bash

# List of screen files to update
SCREEN_FILES=(
  "lib/screens/enrolled_courses_screen.dart"
  "lib/screens/settings_screen.dart"
  "lib/screens/profile_screen.dart"
  "lib/screens/loading_screen.dart"
  "lib/screens/offline_screen.dart"
  "lib/screens/home_screen.dart"
)

for file in "${SCREEN_FILES[@]}"; do
  echo "Updating $file..."
  
  # Remove the import
  sed -i '' 's/import.*screen_security.dart.*;//g' "$file"
  
  # Replace SecureScreen with Scaffold
  sed -i '' 's/return SecureScreen(/return Scaffold(/g' "$file"
  sed -i '' 's/child: SecureScreen(/child: Scaffold(/g' "$file"
  
  # Remove the enforceProtection parameter and child wrapper
  sed -i '' 's/enforceProtection: true,//g' "$file"
  sed -i '' 's/child: Scaffold(/Scaffold(/g' "$file"
  
  # Fix any closing parentheses issues
  sed -i '' 's/),\n      ),\n    );/),\n    );/g' "$file"
  
  echo "Done updating $file"
done

echo "All files updated successfully!"
